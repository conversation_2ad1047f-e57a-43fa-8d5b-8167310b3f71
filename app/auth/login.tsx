import { Link, router } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function AdminLogin() {
  const handleLogin = () => {
    // Simulate login success and redirect to admin dashboard
    router.push('/admin/dashboard');
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>MEENA Admin Login</Text>
        <Text style={styles.subtitle}>Secure Administrative Access</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This is the Admin Login Screen for the MEENA Social Network Admin Web Application. 
          It provides secure authentication for administrators, moderators, and support staff.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Email and password input fields</Text>
          <Text style={styles.component}>• Two-Factor Authentication (2FA) toggle</Text>
          <Text style={styles.component}>• Language selector</Text>
          <Text style={styles.component}>• "Remember me" option</Text>
          <Text style={styles.component}>• "Forgot password?" link</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
          <TouchableOpacity style={styles.primaryButton} onPress={handleLogin}>
            <Text style={styles.primaryButtonText}>Login (Demo)</Text>
          </TouchableOpacity>

          <Link href="/auth/password-recovery" asChild>
            <TouchableOpacity style={styles.secondaryButton}>
              <Text style={styles.secondaryButtonText}>Forgot Password?</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Successful login → Admin Dashboard
          {'\n'}• "Forgot password?" → Password Recovery Screen
        </Text>

        <Text style={styles.sectionTitle}>Role-Based Access Control:</Text>
        <View style={styles.roleList}>
          <Text style={styles.role}>• Super Admin: Full access, delete admins, change policies</Text>
          <Text style={styles.role}>• Senior Moderator: Ban users/groups, approve verifications</Text>
          <Text style={styles.role}>• Junior Moderator: Review reports, warn users, flag content</Text>
          <Text style={styles.role}>• Support Agent: View profiles, reset 2FA, respond to tickets</Text>
          <Text style={styles.role}>• Finance Officer: View payments, export reports, manage ads</Text>
          <Text style={styles.role}>• Compliance Officer: Handle legal requests, manage takedowns</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#2c3e50',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#ecf0f1',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  navigationSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  primaryButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  secondaryButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  roleList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  role: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
