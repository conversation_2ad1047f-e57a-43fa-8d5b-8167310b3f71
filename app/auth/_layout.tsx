import { Stack } from 'expo-router';

export default function AuthLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="login" 
        options={{ 
          title: 'Admin Login',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="password-recovery" 
        options={{ 
          title: 'Password Recovery',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="new-password" 
        options={{ 
          title: 'Set New Password',
          headerShown: false 
        }} 
      />
    </Stack>
  );
}
