import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function TicketManagement() {
  const tickets = [
    { id: 'TKT001', subject: 'Cannot login', user: 'user001', priority: 'High', status: 'Open' },
    { id: 'TKT002', subject: 'Payment issue', user: 'user002', priority: 'High', status: 'In Progress' },
    { id: 'TKT003', subject: 'How to create a group?', user: 'user003', priority: 'Low', status: 'Open' },
    { id: 'TKT004', subject: 'Feature request', user: 'user004', priority: 'Medium', status: 'Open' },
  ];

  const ticketPriorities = [
    { name: 'High Priority', description: 'Account access, payment issues, security concerns' },
    { name: 'Medium Priority', description: 'Feature problems, verification issues' },
    { name: 'Low Priority', description: 'General questions, feature requests' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Ticket Management</Text>
        <Text style={styles.subtitle}>Support Ticket Processing and Resolution Tracking</Text>
        <Link href="/admin/support/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Support</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing support tickets, with tools for priority-based sorting,
          agent assignment, and status tracking.
        </Text>

        <Text style={styles.sectionTitle}>Ticket Queue:</Text>
        {tickets.map((ticket) => (
          <TouchableOpacity key={ticket.id} style={styles.ticketCard}>
            <View style={styles.ticketHeader}>
              <Text style={styles.ticketSubject}>{ticket.subject}</Text>
              <Text style={styles.ticketPriority}>{ticket.priority}</Text>
            </View>
            <Text style={styles.ticketDetails}>User: {ticket.user} | Status: {ticket.status}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Ticket Priorities:</Text>
        <View style={styles.prioritiesContainer}>
          {ticketPriorities.map((priority, index) => (
            <View key={index} style={styles.priorityCard}>
              <Text style={styles.priorityName}>{priority.name}</Text>
              <Text style={styles.priorityDescription}>{priority.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Ticket queue management</Text>
          <Text style={styles.feature}>• Priority-based sorting</Text>
          <Text style={styles.feature}>• Agent assignment system</Text>
          <Text style={styles.feature}>• Status tracking</Text>
          <Text style={styles.feature}>• Customer communication tools</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Ticket Detail → Individual ticket management with conversation history
          {
            '\n'}
          • Agent Assignment → Ticket routing system and agent workload overview
          {
            '\n'}
          • Customer Communication → User notification tools for ticket updates
          {
            '\n'}
          • Resolution Tracking → Ticket status monitoring and reporting
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  ticketCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  ticketHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  ticketSubject: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  ticketPriority: { fontSize: 14, color: '#e74c3c', fontWeight: 'bold' },
  ticketDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  prioritiesContainer: { marginBottom: 16 },
  priorityCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  priorityName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  priorityDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});