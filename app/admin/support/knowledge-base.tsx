import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function KnowledgeBase() {
  const knowledgeCategories = [
    { name: 'Getting Started', description: 'Platform introduction and basic features' },
    { name: 'Account Management', description: 'Profile, security, and settings' },
    { name: 'Content Guidelines', description: 'Community rules and content policies' },
    { name: 'Technical Support', description: 'Troubleshooting and technical issues' },
  ];

  const articles = [
    { id: 'kb001', title: 'How to create a group', category: 'Getting Started', views: 1200 },
    { id: 'kb002', title: 'How to reset your password', category: 'Account Management', views: 2500 },
    { id: 'kb003', title: 'Understanding our content policies', category: 'Content Guidelines', views: 800 },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Knowledge Base</Text>
        <Text style={styles.subtitle}>Help Documentation and Article Management</Text>
        <Link href="/admin/support/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Support</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing the help documentation and articles in the knowledge base.
          It includes tools for article creation, category organization, and usage analytics.
        </Text>

        <Text style={styles.sectionTitle}>Knowledge Categories:</Text>
        <View style={styles.categoriesContainer}>
          {knowledgeCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Popular Articles:</Text>
        {articles.map((article) => (
          <TouchableOpacity key={article.id} style={styles.articleCard}>
            <Text style={styles.articleTitle}>{article.title}</Text>
            <Text style={styles.articleDetails}>Category: {article.category} | Views: {article.views}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Article creation and editing</Text>
          <Text style={styles.feature}>• Category organization</Text>
          <Text style={styles.feature}>• Search functionality</Text>
          <Text style={styles.feature}>• Usage analytics</Text>
          <Text style={styles.feature}>• Multi-language support</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Article Editor → Article creation and modification with rich text editor
          {
            '\n'}
          • Category Management → Knowledge base organization and hierarchy
          {
            '\n'}
          • Usage Analytics → Article view and search statistics
          {
            '\n'}
          • Translation Management → Multi-language content management
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#27ae60', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#27ae60' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  articleCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  articleTitle: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  articleDetails: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});