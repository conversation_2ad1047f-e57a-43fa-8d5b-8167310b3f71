import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function FeatureRequests() {
  const featureRequests = [
    { id: 'fr001', title: 'Dark mode for the app', category: 'User Interface', votes: 1500, status: 'In Progress' },
    { id: 'fr002', title: 'Ability to schedule posts', category: 'Functionality', votes: 800, status: 'Planned' },
    { id: 'fr003', title: 'Improve video loading speed', category: 'Performance', votes: 500, status: 'Under Review' },
    { id: 'fr004', title: 'End-to-end encryption for DMs', category: 'Security', votes: 2000, status: 'In Progress' },
  ];

  const requestCategories = [
    { name: 'User Interface', description: 'Platform design and usability improvements' },
    { name: 'Functionality', description: 'New features and capabilities' },
    { name: 'Performance', description: 'Speed and efficiency enhancements' },
    { name: 'Security', description: 'Privacy and security feature requests' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Feature Requests</Text>
        <Text style={styles.subtitle}>User-Submitted Feature Suggestions</Text>
        <Link href="/admin/support/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Support</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing user-submitted feature suggestions. It includes a community
          voting system, priority ranking, and development status tracking.
        </Text>

        <Text style={styles.sectionTitle}>Feature Requests:</Text>
        {featureRequests.map((req) => (
          <TouchableOpacity key={req.id} style={styles.requestCard}>
            <Text style={styles.requestTitle}>{req.title}</Text>
            <Text style={styles.requestDetails}>Category: {req.category} | Votes: {req.votes}</Text>
            <Text style={styles.requestStatus}>Status: {req.status}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Request Categories:</Text>
        <View style={styles.categoriesContainer}>
          {requestCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Feature request collection</Text>
          <Text style={styles.feature}>• Community voting system</Text>
          <Text style={styles.feature}>• Priority ranking</Text>
          <Text style={styles.feature}>• Development status tracking</Text>
          <Text style={styles.feature}>• User feedback integration</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Request Review → Individual feature request analysis with user comments
          {
            '\n'}
          • Voting System → Community feedback management and vote tracking
          {
            '\n'}
          • Development Tracking → Feature implementation status and roadmap
          {
            '\n'}
          • User Communication → Request status updates and announcements
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  requestCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  requestTitle: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  requestDetails: { fontSize: 12, color: '#555', marginVertical: 4 },
  requestStatus: { fontSize: 14, color: '#27ae60', fontWeight: 'bold' },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});