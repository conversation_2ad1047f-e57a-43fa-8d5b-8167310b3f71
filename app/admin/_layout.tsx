import { Stack } from 'expo-router';

export default function AdminLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Admin Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="alert-center" 
        options={{ 
          title: 'Alert Center',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="users" 
        options={{ 
          title: 'User Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="content" 
        options={{ 
          title: 'Content Moderation',
          headerShown: false 
        }} 
      />
      <Stack.Screen
        name="groups"
        options={{
          title: 'Group Management',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="channels"
        options={{
          title: 'Channel Management',
          headerShown: false
        }}
      />
      <Stack.Screen 
        name="payments" 
        options={{ 
          title: 'Payment Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="policies" 
        options={{ 
          title: 'Policy Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="system" 
        options={{ 
          title: 'System Settings',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="analytics" 
        options={{ 
          title: 'Analytics & Reporting',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="support" 
        options={{ 
          title: 'Support & Helpdesk',
          headerShown: false 
        }} 
      />
      <Stack.Screen
        name="profile"
        options={{
          title: 'Admin Profile',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="advertising"
        options={{
          title: 'Advertising Management',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="remote-wipe"
        options={{
          title: 'Remote Wipe Management',
          headerShown: false
        }}
      />
    </Stack>
  );
}
