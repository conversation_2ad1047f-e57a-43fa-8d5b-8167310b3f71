import { Stack } from 'expo-router';

export default function AdvertisingLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Advertising Management',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="dashboard" 
        options={{ 
          title: 'Advertising Dashboard',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="ad-review" 
        options={{ 
          title: 'Ad Review',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="ad-creation" 
        options={{ 
          title: 'Ad Creation',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="campaign-management" 
        options={{ 
          title: 'Campaign Management',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="revenue-tracking" 
        options={{ 
          title: 'Revenue Tracking',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="[id]" 
        options={{ 
          title: 'Ad Details',
          headerStyle: { backgroundColor: '#e67e22' },
          headerTintColor: '#fff',
        }} 
      />
    </Stack>
  );
}
