import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RevenueTracking() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Revenue Tracking</Text>
        <Text style={styles.subtitle}>Track advertising revenue and performance metrics</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Track advertising revenue, payment method analysis, campaign performance metrics, 
          and financial trend analysis. Provides comprehensive revenue insights for 
          advertising operations and business intelligence.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Revenue breakdown</Text>
          <Text style={styles.component}>• Payment method analysis</Text>
          <Text style={styles.component}>• Campaign performance metrics</Text>
          <Text style={styles.component}>• Time period selection</Text>
          <Text style={styles.component}>• Export options</Text>
        </View>

        <Text style={styles.sectionTitle}>Revenue Overview:</Text>
        <View style={styles.revenueGrid}>
          <View style={styles.revenueCard}>
            <Text style={styles.revenueValue}>€125,430</Text>
            <Text style={styles.revenueLabel}>Total Revenue (This Month)</Text>
            <Text style={styles.revenueChange}>+12.5% from last month</Text>
          </View>
          <View style={styles.revenueCard}>
            <Text style={styles.revenueValue}>€4,180</Text>
            <Text style={styles.revenueLabel}>Daily Average</Text>
            <Text style={styles.revenueChange}>+8.3% from yesterday</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Revenue by Ad Format:</Text>
        <View style={styles.formatRevenue}>
          <View style={styles.formatCard}>
            <Text style={styles.formatName}>Banner Ads</Text>
            <Text style={styles.formatRevenue}>€45,200</Text>
            <Text style={styles.formatPercentage}>36% of total revenue</Text>
          </View>
          <View style={styles.formatCard}>
            <Text style={styles.formatName}>Feed Post Ads</Text>
            <Text style={styles.formatRevenue}>€52,100</Text>
            <Text style={styles.formatPercentage}>42% of total revenue</Text>
          </View>
          <View style={styles.formatCard}>
            <Text style={styles.formatName}>Story Ads</Text>
            <Text style={styles.formatRevenue}>€28,130</Text>
            <Text style={styles.formatPercentage}>22% of total revenue</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Story Ad Revenue Tracking:</Text>
        <View style={styles.storyRevenueCard}>
          <Text style={styles.storyRevenueTitle}>Story Ad Performance</Text>
          <View style={styles.storyMetrics}>
            <View style={styles.storyMetric}>
              <Text style={styles.storyMetricValue}>€28,130</Text>
              <Text style={styles.storyMetricLabel}>Story Ad Revenue</Text>
            </View>
            <View style={styles.storyMetric}>
              <Text style={styles.storyMetricValue}>1,245</Text>
              <Text style={styles.storyMetricLabel}>Story Placements</Text>
            </View>
            <View style={styles.storyMetric}>
              <Text style={styles.storyMetricValue}>€22.60</Text>
              <Text style={styles.storyMetricLabel}>Avg. Revenue per Story</Text>
            </View>
          </View>
          <Text style={styles.storyNote}>
            Full-screen format with audio support generating premium advertising rates
          </Text>
        </View>

        <Text style={styles.sectionTitle}>Payment Method Analysis:</Text>
        <View style={styles.paymentMethods}>
          <View style={styles.paymentCard}>
            <Text style={styles.paymentMethod}>Credit Cards</Text>
            <Text style={styles.paymentAmount}>€89,200 (71%)</Text>
          </View>
          <View style={styles.paymentCard}>
            <Text style={styles.paymentMethod}>Bank Transfers</Text>
            <Text style={styles.paymentAmount}>€24,100 (19%)</Text>
          </View>
          <View style={styles.paymentCard}>
            <Text style={styles.paymentMethod}>Digital Wallets</Text>
            <Text style={styles.paymentAmount}>€12,130 (10%)</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Revenue detail → Revenue Detail Screen</Text>
          <Text style={styles.navItem}>• Revenue projection → Revenue Projection Screen</Text>
          <Text style={styles.navItem}>• Payment method analysis → Payment Method Analysis Screen</Text>
          <Text style={styles.navItem}>• Analytics dashboard → Analytics Dashboard Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Export & Reporting:</Text>
        <View style={styles.exportOptions}>
          <TouchableOpacity style={styles.exportButton}>
            <Text style={styles.exportButtonText}>Export PDF Report</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.exportButton}>
            <Text style={styles.exportButtonText}>Export CSV Data</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.exportButton}>
            <Text style={styles.exportButtonText}>Schedule Report</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.exportButton}>
            <Text style={styles.exportButtonText}>Revenue Forecast</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Key Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• Real-time revenue tracking</Text>
          <Text style={styles.feature}>• Revenue breakdown by ad format</Text>
          <Text style={styles.feature}>• Story ad revenue tracking with premium rates</Text>
          <Text style={styles.feature}>• Payment method analysis</Text>
          <Text style={styles.feature}>• Financial trend analysis</Text>
          <Text style={styles.feature}>• Automated reporting and exports</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  revenueGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  revenueCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
  },
  revenueValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#27ae60',
    marginBottom: 4,
  },
  revenueLabel: {
    fontSize: 12,
    color: '#555',
    marginBottom: 4,
  },
  revenueChange: {
    fontSize: 11,
    color: '#27ae60',
    fontWeight: 'bold',
  },
  formatRevenue: {
    marginBottom: 16,
  },
  formatCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
  },
  formatName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  formatPercentage: {
    fontSize: 12,
    color: '#7f8c8d',
  },
  storyRevenueCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#9b59b6',
  },
  storyRevenueTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#9b59b6',
    marginBottom: 12,
  },
  storyMetrics: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  storyMetric: {
    alignItems: 'center',
  },
  storyMetricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#9b59b6',
  },
  storyMetricLabel: {
    fontSize: 10,
    color: '#555',
    textAlign: 'center',
  },
  storyNote: {
    fontSize: 12,
    color: '#7f8c8d',
    fontStyle: 'italic',
  },
  paymentMethods: {
    marginBottom: 16,
  },
  paymentCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  paymentMethod: {
    fontSize: 14,
    color: '#555',
  },
  paymentAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  exportOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  exportButton: {
    backgroundColor: '#3498db',
    padding: 12,
    borderRadius: 6,
    width: '48%',
    marginBottom: 8,
    alignItems: 'center',
  },
  exportButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
