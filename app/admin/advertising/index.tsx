import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdvertisingIndex() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Advertising Management</Text>
        <Text style={styles.subtitle}>Manage ads, campaigns, and revenue tracking</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>System Overview:</Text>
        <Text style={styles.description}>
          The Advertising Management System provides comprehensive tools for managing 
          advertising content, campaigns, and revenue tracking across the MEENA platform.
        </Text>

        <Text style={styles.sectionTitle}>Available Modules:</Text>
        
        <Link href="/admin/advertising/dashboard" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Advertising Dashboard</Text>
            <Text style={styles.moduleDescription}>
              Active ads overview, pending approvals, expired campaigns, revenue statistics
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/advertising/ad-review" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Ad Review</Text>
            <Text style={styles.moduleDescription}>
              Review ad content, approve/reject ads, flag for legal review
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/advertising/ad-creation" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Ad Creation</Text>
            <Text style={styles.moduleDescription}>
              Create new ads with banner, story, and feed formats
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/advertising/campaign-management" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Campaign Management</Text>
            <Text style={styles.moduleDescription}>
              Manage advertising campaigns, performance metrics, budget tracking
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/advertising/revenue-tracking" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Revenue Tracking</Text>
            <Text style={styles.moduleDescription}>
              Track advertising revenue, payment analysis, campaign performance
            </Text>
          </TouchableOpacity>
        </Link>

        <Text style={styles.sectionTitle}>Key Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• Ad review workflow for all advertising content</Text>
          <Text style={styles.feature}>• Revenue tracking for all advertising transactions</Text>
          <Text style={styles.feature}>• Campaign management tools for advertisers</Text>
          <Text style={styles.feature}>• Support for Story ads with full-screen format and audio</Text>
          <Text style={styles.feature}>• Revenue tracking for Story ad placements</Text>
          <Text style={styles.feature}>• Compliance monitoring for advertising content</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  moduleCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  moduleName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 8,
  },
  moduleDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
