import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function CampaignManagement() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Campaign Management</Text>
        <Text style={styles.subtitle}>Manage advertising campaigns and performance</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Manage advertising campaigns including performance metrics, budget tracking, 
          and campaign lifecycle management. Monitor campaign effectiveness and make 
          real-time adjustments to optimize advertising revenue.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Campaign listing</Text>
          <Text style={styles.component}>• Performance metrics</Text>
          <Text style={styles.component}>• Budget tracking</Text>
          <Text style={styles.component}>• Filter options (status, date)</Text>
          <Text style={styles.component}>• Campaign details preview</Text>
        </View>

        <Text style={styles.sectionTitle}>Campaign Performance Metrics:</Text>
        <View style={styles.metricsGrid}>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>156</Text>
            <Text style={styles.metricLabel}>Active Campaigns</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>€45,230</Text>
            <Text style={styles.metricLabel}>Total Budget</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>€12,450</Text>
            <Text style={styles.metricLabel}>Spent Today</Text>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricValue}>8.5%</Text>
            <Text style={styles.metricLabel}>Avg. CTR</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Campaign Types:</Text>
        <View style={styles.campaignTypes}>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Banner Campaigns</Text>
            <Text style={styles.typeDescription}>Standard display advertising campaigns</Text>
            <Text style={styles.typeStats}>45 active • €15,200 budget</Text>
          </View>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Feed Post Campaigns</Text>
            <Text style={styles.typeDescription}>Native content advertising campaigns</Text>
            <Text style={styles.typeStats}>67 active • €18,900 budget</Text>
          </View>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Story Campaigns</Text>
            <Text style={styles.typeDescription}>Full-screen story advertising campaigns</Text>
            <Text style={styles.typeStats}>44 active • €11,130 budget</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Campaign selection → Campaign Details Screen</Text>
          <Text style={styles.navItem}>• Budget adjustment → Budget Adjustment Screen</Text>
          <Text style={styles.navItem}>• Target audience update → Audience Update Screen</Text>
          <Text style={styles.navItem}>• Campaign termination → Termination Confirmation Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Campaign Management Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Create Campaign</Text>
            <Text style={styles.actionDescription}>Launch new advertising campaign</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Budget Overview</Text>
            <Text style={styles.actionDescription}>View budget allocation and spending</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Performance Analytics</Text>
            <Text style={styles.actionDescription}>Detailed campaign performance metrics</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Audience Insights</Text>
            <Text style={styles.actionDescription}>Target audience analysis and optimization</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Campaign Status Types:</Text>
        <View style={styles.statusList}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#27ae60' }]} />
            <Text style={styles.statusText}>Active: Campaign is running</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#f39c12' }]} />
            <Text style={styles.statusText}>Paused: Campaign temporarily stopped</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#3498db' }]} />
            <Text style={styles.statusText}>Scheduled: Campaign set to start later</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#95a5a6' }]} />
            <Text style={styles.statusText}>Completed: Campaign finished</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Key Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• Real-time campaign performance monitoring</Text>
          <Text style={styles.feature}>• Budget tracking and optimization</Text>
          <Text style={styles.feature}>• Target audience management</Text>
          <Text style={styles.feature}>• A/B testing capabilities</Text>
          <Text style={styles.feature}>• ROI analysis and reporting</Text>
          <Text style={styles.feature}>• Campaign scheduling and automation</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  metricCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  metricLabel: {
    fontSize: 12,
    color: '#555',
    textAlign: 'center',
  },
  campaignTypes: {
    marginBottom: 16,
  },
  typeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
  },
  typeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  typeDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  typeStats: {
    fontSize: 12,
    color: '#7f8c8d',
    fontWeight: 'bold',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
  statusList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
