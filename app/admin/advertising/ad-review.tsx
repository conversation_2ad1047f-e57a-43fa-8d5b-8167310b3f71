import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdReview() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Ad Review</Text>
        <Text style={styles.subtitle}>Review and approve advertising content</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Review advertising content including banner ads, story ads, and feed posts. 
          Approve, reject, or flag ads for legal review based on content guidelines.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Ad preview (banner, story, feed)</Text>
          <Text style={styles.component}>• Advertiser information</Text>
          <Text style={styles.component}>• Target audience details</Text>
          <Text style={styles.component}>• Landing URL</Text>
          <Text style={styles.component}>• Ad Type Indicator: [Banner, Feed Post, Story]</Text>
          <Text style={styles.component}>• Moderation options (Approve, Reject, Flag for legal review)</Text>
          <Text style={styles.component}>• Status indicators</Text>
        </View>

        <Text style={styles.sectionTitle}>Ad Format Support:</Text>
        <View style={styles.formatList}>
          <View style={styles.formatItem}>
            <Text style={styles.formatName}>Banner Ads</Text>
            <Text style={styles.formatDescription}>Standard display advertising with image/text</Text>
          </View>
          <View style={styles.formatItem}>
            <Text style={styles.formatName}>Feed Post Ads</Text>
            <Text style={styles.formatDescription}>Native content advertising in user feeds</Text>
          </View>
          <View style={styles.formatItem}>
            <Text style={styles.formatName}>Story Ads</Text>
            <Text style={styles.formatDescription}>Full-screen format with audio support (5-15 sec duration)</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Review Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionTitle}>Approve</Text>
            <Text style={styles.actionDescription}>Approve ad for publication</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#e74c3c' }]}>
            <Text style={styles.actionTitle}>Reject</Text>
            <Text style={styles.actionDescription}>Reject with reason</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#f39c12' }]}>
            <Text style={styles.actionTitle}>Flag Legal</Text>
            <Text style={styles.actionDescription}>Flag for legal review</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionTitle}>Request Info</Text>
            <Text style={styles.actionDescription}>Request more information</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Back → Advertising Dashboard</Text>
          <Text style={styles.navItem}>• Campaign details → Campaign Details Screen</Text>
          <Text style={styles.navItem}>• Advertiser details → Advertiser Details Screen</Text>
          <Text style={styles.navItem}>• Rejection reason → Rejection Reason Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Story Ad Specific Features:</Text>
        <View style={styles.storyFeatures}>
          <Text style={styles.feature}>• Duration validation (5-15 seconds)</Text>
          <Text style={styles.feature}>• Full-screen preview capability</Text>
          <Text style={styles.feature}>• Music overlay toggle and review</Text>
          <Text style={styles.feature}>• Audio content compliance check</Text>
          <Text style={styles.feature}>• Interactive element validation</Text>
        </View>

        <Text style={styles.sectionTitle}>Compliance Guidelines:</Text>
        <View style={styles.complianceList}>
          <Text style={styles.compliance}>• Content must comply with community standards</Text>
          <Text style={styles.compliance}>• No misleading or false advertising claims</Text>
          <Text style={styles.compliance}>• Age-appropriate content based on target audience</Text>
          <Text style={styles.compliance}>• Respect intellectual property rights</Text>
          <Text style={styles.compliance}>• Comply with regional advertising regulations</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  formatList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  formatItem: {
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  formatName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  formatDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  storyFeatures: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  complianceList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  compliance: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
