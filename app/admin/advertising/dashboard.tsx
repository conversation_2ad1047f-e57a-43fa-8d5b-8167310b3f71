import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdvertisingDashboard() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Advertising Dashboard</Text>
        <Text style={styles.subtitle}>Overview of advertising activities and metrics</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The Advertising Dashboard provides a comprehensive overview of all advertising 
          activities including active ads, pending approvals, expired campaigns, and 
          revenue statistics.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Active ads overview</Text>
          <Text style={styles.component}>• Pending approvals</Text>
          <Text style={styles.component}>• Expired campaigns</Text>
          <Text style={styles.component}>• Revenue statistics</Text>
          <Text style={styles.component}>• Filter options (status, date range, ad format)</Text>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionGrid}>
          <Link href="/admin/advertising/ad-review" asChild>
            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionTitle}>Review Ads</Text>
              <Text style={styles.actionDescription}>Review pending ad approvals</Text>
            </TouchableOpacity>
          </Link>

          <Link href="/admin/advertising/ad-creation" asChild>
            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionTitle}>Create Ad</Text>
              <Text style={styles.actionDescription}>Create new advertisement</Text>
            </TouchableOpacity>
          </Link>

          <Link href="/admin/advertising/campaign-management" asChild>
            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionTitle}>Manage Campaigns</Text>
              <Text style={styles.actionDescription}>View and manage campaigns</Text>
            </TouchableOpacity>
          </Link>

          <Link href="/admin/advertising/revenue-tracking" asChild>
            <TouchableOpacity style={styles.actionCard}>
              <Text style={styles.actionTitle}>Revenue Tracking</Text>
              <Text style={styles.actionDescription}>Track advertising revenue</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Ad selection → Ad Review Screen</Text>
          <Text style={styles.navItem}>• New ad → Ad Creation Screen</Text>
          <Text style={styles.navItem}>• Campaign management → Campaign Management Screen</Text>
          <Text style={styles.navItem}>• Revenue tracking → Revenue Tracking Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Ad Format Support:</Text>
        <View style={styles.formatList}>
          <Text style={styles.format}>• Banner Ads: Standard display advertising</Text>
          <Text style={styles.format}>• Feed Post Ads: Native content advertising</Text>
          <Text style={styles.format}>• Story Ads: Full-screen format with audio support</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  formatList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  format: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
