import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AdCreation() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Ad Creation</Text>
        <Text style={styles.subtitle}>Create new advertisements with advanced options</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Create new advertisements with support for banner, story, and feed formats. 
          Includes story-specific options like duration control, full-screen preview, 
          and music overlay toggle for enhanced user engagement.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Ad type selection (banner, story, feed)</Text>
          <Text style={styles.component}>• Story-specific options: Duration (5-15 sec), Full-screen preview, Music overlay toggle</Text>
          <Text style={styles.component}>• Media upload interface</Text>
          <Text style={styles.component}>• Target audience configuration</Text>
          <Text style={styles.component}>• Budget and duration settings</Text>
          <Text style={styles.component}>• Preview option</Text>
        </View>

        <Text style={styles.sectionTitle}>Ad Type Selection:</Text>
        <View style={styles.adTypeGrid}>
          <TouchableOpacity style={styles.adTypeCard}>
            <Text style={styles.adTypeName}>Banner Ad</Text>
            <Text style={styles.adTypeDescription}>Standard display advertising</Text>
            <Text style={styles.adTypeSpecs}>• Image/text format • Static or animated • Multiple sizes</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.adTypeCard}>
            <Text style={styles.adTypeName}>Feed Post Ad</Text>
            <Text style={styles.adTypeDescription}>Native content advertising</Text>
            <Text style={styles.adTypeSpecs}>• Blends with user content • Image/video support • Engagement focused</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.adTypeCard, styles.storyAdCard]}>
            <Text style={styles.adTypeName}>Story Ad</Text>
            <Text style={styles.adTypeDescription}>Full-screen immersive advertising</Text>
            <Text style={styles.adTypeSpecs}>• Full-screen format • Audio support • Premium engagement</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Story Ad Specific Options:</Text>
        <View style={styles.storyOptionsCard}>
          <Text style={styles.storyOptionsTitle}>Enhanced Story Ad Features</Text>
          
          <View style={styles.storyOption}>
            <Text style={styles.optionLabel}>Duration Control:</Text>
            <Text style={styles.optionValue}>5-15 seconds (adjustable)</Text>
            <Text style={styles.optionDescription}>Optimal engagement duration for story format</Text>
          </View>

          <View style={styles.storyOption}>
            <Text style={styles.optionLabel}>Full-Screen Preview:</Text>
            <Text style={styles.optionValue}>Real-time preview available</Text>
            <Text style={styles.optionDescription}>Preview exactly how users will see the story ad</Text>
          </View>

          <View style={styles.storyOption}>
            <Text style={styles.optionLabel}>Music Overlay Toggle:</Text>
            <Text style={styles.optionValue}>Audio enhancement enabled</Text>
            <Text style={styles.optionDescription}>Add background music or audio effects</Text>
          </View>

          <View style={styles.storyOption}>
            <Text style={styles.optionLabel}>Interactive Elements:</Text>
            <Text style={styles.optionValue}>Swipe-up actions, tap interactions</Text>
            <Text style={styles.optionDescription}>Enhanced user engagement options</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Media Upload Interface:</Text>
        <View style={styles.uploadSection}>
          <TouchableOpacity style={styles.uploadButton}>
            <Text style={styles.uploadButtonText}>Upload Image/Video</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.uploadButton}>
            <Text style={styles.uploadButtonText}>Upload Audio (Story Ads)</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.uploadButton}>
            <Text style={styles.uploadButtonText}>Select from Library</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Target Audience Configuration:</Text>
        <View style={styles.audienceConfig}>
          <View style={styles.configOption}>
            <Text style={styles.configLabel}>Age Range:</Text>
            <Text style={styles.configValue}>18-65 (adjustable)</Text>
          </View>
          <View style={styles.configOption}>
            <Text style={styles.configLabel}>Geographic Location:</Text>
            <Text style={styles.configValue}>Global/Regional/Local</Text>
          </View>
          <View style={styles.configOption}>
            <Text style={styles.configLabel}>Interests:</Text>
            <Text style={styles.configValue}>Technology, Business, Entertainment</Text>
          </View>
          <View style={styles.configOption}>
            <Text style={styles.configLabel}>Device Type:</Text>
            <Text style={styles.configValue}>Mobile, Desktop, Tablet</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Budget & Duration Settings:</Text>
        <View style={styles.budgetSettings}>
          <View style={styles.budgetOption}>
            <Text style={styles.budgetLabel}>Daily Budget:</Text>
            <Text style={styles.budgetValue}>€50 - €5,000</Text>
          </View>
          <View style={styles.budgetOption}>
            <Text style={styles.budgetLabel}>Campaign Duration:</Text>
            <Text style={styles.budgetValue}>1 day - 90 days</Text>
          </View>
          <View style={styles.budgetOption}>
            <Text style={styles.budgetLabel}>Bid Strategy:</Text>
            <Text style={styles.budgetValue}>Automatic/Manual CPC</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Preview → Ad Preview Screen</Text>
          <Text style={styles.navItem}>• Submission → Submission Confirmation Screen</Text>
          <Text style={styles.navItem}>• Back → Advertising Dashboard</Text>
        </View>

        <Text style={styles.sectionTitle}>Creation Actions:</Text>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.previewButton}>
            <Text style={styles.previewButtonText}>Preview Ad</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.saveButton}>
            <Text style={styles.saveButtonText}>Save Draft</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.submitButton}>
            <Text style={styles.submitButtonText}>Submit for Review</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e67e22',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  adTypeGrid: {
    marginBottom: 16,
  },
  adTypeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e67e22',
  },
  storyAdCard: {
    borderLeftColor: '#9b59b6',
  },
  adTypeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e67e22',
    marginBottom: 4,
  },
  adTypeDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  adTypeSpecs: {
    fontSize: 12,
    color: '#7f8c8d',
    lineHeight: 16,
  },
  storyOptionsCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#9b59b6',
  },
  storyOptionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#9b59b6',
    marginBottom: 16,
  },
  storyOption: {
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  optionLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 4,
  },
  optionValue: {
    fontSize: 14,
    color: '#9b59b6',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    lineHeight: 16,
  },
  uploadSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  uploadButton: {
    backgroundColor: '#3498db',
    padding: 12,
    borderRadius: 6,
    width: '32%',
    alignItems: 'center',
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  audienceConfig: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  configOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  configLabel: {
    fontSize: 14,
    color: '#555',
  },
  configValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#3498db',
  },
  budgetSettings: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  budgetOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  budgetLabel: {
    fontSize: 14,
    color: '#555',
  },
  budgetValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  previewButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  previewButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  saveButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  submitButton: {
    backgroundColor: '#27ae60',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
