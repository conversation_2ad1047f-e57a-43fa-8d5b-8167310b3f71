import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ChannelManagementDashboard() {
  const keyMetrics = [
    { label: 'Total Channels', value: '12,345', color: '#e67e22' },
    { label: 'Monetized Channels', value: '1,234', color: '#27ae60' },
    { label: 'Subscribers', value: '1.2M', color: '#3498db' },
    { label: 'Monthly Revenue', value: '€45,000', color: '#f1c40f' },
  ];

  const channelTypes = [
    { name: 'Public Channels', description: 'Open subscription, broadcast content' },
    { name: 'Private Channels', description: 'Invite-only, premium content' },
    { name: 'Secret Channels', description: 'Hidden, manual review required' },
  ];

  const mainTasks = [
      { 
        title: 'Channel Directory', 
        route: '/admin/channels/directory', 
        color: '#3498db',
        description: 'Search, filter, and manage all channels'
    }, { 
        title: 'Creation Requests', 
        route: '/admin/channels/creation-requests', 
        color: '#27ae60',
        description: 'Approve or deny new channel creation requests'
    }, { 
        title: 'Secret Channels Monitoring', 
        route: '/admin/channels/secret-monitoring', 
        color: '#e67e22',
        description: 'Monitor activity in secret channels for violations'
    }, { 
        title: 'Banned Channels', 
        route: '/admin/channels/banned-channels', 
        color: '#e74c3c',
        description: 'Manage banned channels and review appeals'
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Channel Management Dashboard</Text>
        <Text style={styles.subtitle}>Statistics and Monetization Tracking</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This dashboard provides an overview of channel statistics, monetization tracking, and
          provides access to various channel management tools.
        </Text>

        <Text style={styles.sectionTitle}>Key Metrics:</Text>
        <View style={styles.metricsGrid}>
          {keyMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Main Tasks:</Text>
        {mainTasks.map((task, index) => (
          <Link key={index} href={task.route} asChild>
            <TouchableOpacity style={[styles.taskCard, { borderLeftColor: task.color }]}>
                <Text style={[styles.taskTitle, { color: task.color }]}>{task.title}</Text>
                <Text style={styles.taskDescription}>{task.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Channel Types:</Text>
        <View style={styles.typesContainer}>
          {channelTypes.map((type, index) => (
            <View key={index} style={styles.typeCard}>
              <Text style={styles.typeName}>{type.name}</Text>
              <Text style={styles.typeDescription}>{type.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Channel type distribution analytics</Text>
          <Text style={styles.feature}>• Monetization status tracking</Text>
          <Text style={styles.feature}>• Subscriber growth metrics</Text>
          <Text style={styles.feature}>• Content performance analytics</Text>
          <Text style={styles.feature}>• Revenue generation reports</Text>
        </View>

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e67e22', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, borderTopWidth: 4, alignItems: 'center' },
  metricValue: { fontSize: 22, fontWeight: 'bold', color: '#2c3e50' },
  metricLabel: { fontSize: 12, color: '#7f8c8d', textAlign: 'center', marginTop: 4 },
  taskCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4 },
  taskTitle: { fontSize: 18, fontWeight: 'bold' },
  taskDescription: { fontSize: 14, color: '#555', marginTop: 4 },
  typesContainer: { marginBottom: 16 },
  typeCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  typeName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  typeDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
});