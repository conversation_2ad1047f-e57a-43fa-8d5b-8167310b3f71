import { Link, useLocalSearchParams } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ChannelDetail() {
  const { id } = useLocalSearchParams();

  const channelData = {
    id: id as string,
    name: 'Tech News Daily',
    type: 'Public',
    description: 'The latest news and analysis in the world of technology.',
    subscribers: 125000,
    monetization: 'Enabled',
    revenue: '€5,000/mo',
    creator: 'user025',
    linkedGroup: 'Tech Enthusiasts',
  };

  const availableGroups = [
    'None',
    'Tech Enthusiasts',
    'Cooking Masters',
    'Photography Club',
    'Gaming Community',
    'Business Network',
  ];

  const recentContent = [
    { id: 'c010', title: 'The Future of AI in 2025', views: 15000, engagement: '8%' },
    { id: 'c011', title: 'New Quantum Computing Breakthrough', views: 25000, engagement: '12%' },
    { id: 'c012', title: 'Top 5 Gadgets of the Year', views: 50000, engagement: '15%' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Channel Detail</Text>
        <Text style={styles.subtitle}>{channelData.name}</Text>
        <Link href="/admin/channels/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides comprehensive information about a specific channel, including
          monetization tools, subscriber management, and content performance analytics.
        </Text>

        <Text style={styles.sectionTitle}>Channel Information:</Text>
        <View style={styles.infoCard}>
          <Text style={styles.infoText}>Name: {channelData.name}</Text>
          <Text style={styles.infoText}>Type: {channelData.type}</Text>
          <Text style={styles.infoText}>Creator: {channelData.creator}</Text>
          <Text style={styles.infoText}>Subscribers: {channelData.subscribers}</Text>
          <Text style={styles.infoText}>Monetization: {channelData.monetization}</Text>
          <Text style={styles.infoText}>Revenue: {channelData.revenue}</Text>
          <Text style={styles.infoText}>Description: {channelData.description}</Text>
        </View>

        <Text style={styles.sectionTitle}>Channel-Group Linking:</Text>
        <View style={styles.linkingCard}>
          <View style={styles.currentLinkContainer}>
            <Text style={styles.linkLabel}>Linked Group: </Text>
            <Text style={[styles.linkValue, {
              color: channelData.linkedGroup === 'None' ? '#95a5a6' : '#3498db'
            }]}>
              {channelData.linkedGroup}
            </Text>
          </View>

          <Text style={styles.linkDescription}>
            Support for linking one channel to one parent group. This creates a connection
            between the channel and group for enhanced community management.
          </Text>

          <View style={styles.linkingActions}>
            <TouchableOpacity style={styles.linkButton}>
              <Text style={styles.linkButtonText}>Link to Group</Text>
            </TouchableOpacity>

            {channelData.linkedGroup !== 'None' && (
              <TouchableOpacity style={styles.unlinkButton}>
                <Text style={styles.unlinkButtonText}>Unlink Group</Text>
              </TouchableOpacity>
            )}
          </View>

          <Text style={styles.availableGroupsTitle}>Available Groups:</Text>
          <View style={styles.groupsList}>
            {availableGroups.map((group, index) => (
              <TouchableOpacity key={index} style={[
                styles.groupOption,
                group === channelData.linkedGroup && styles.selectedGroup
              ]}>
                <Text style={[
                  styles.groupOptionText,
                  group === channelData.linkedGroup && styles.selectedGroupText
                ]}>
                  {group}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <Text style={styles.sectionTitle}>Recent Content:</Text>
        {recentContent.map((item) => (
          <TouchableOpacity key={item.id} style={styles.contentCard}>
            <Text style={styles.contentTitle}>{item.title}</Text>
            <Text style={styles.contentDetails}>Views: {item.views} | Engagement: {item.engagement}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Complete channel profile display</Text>
          <Text style={styles.feature}>• Subscriber management interface</Text>
          <Text style={styles.feature}>• Content monitoring tools</Text>
          <Text style={styles.feature}>• Monetization controls</Text>
          <Text style={styles.feature}>• Performance analytics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Subscriber Profiles → Individual user detail screens for subscriber management
          {
            '\n'}
          • Content Analysis → Content performance metrics and analytics
          {
            '\n'}
          • Revenue Management → Monetization controls and financial reports
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e67e22', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  infoCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  infoText: { fontSize: 14, color: '#555', marginBottom: 8 },
  contentCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  contentTitle: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  contentDetails: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
  linkingCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  currentLinkContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  linkLabel: { fontSize: 14, color: '#7f8c8d' },
  linkValue: { fontSize: 14, fontWeight: 'bold' },
  linkDescription: { fontSize: 14, color: '#555', lineHeight: 20, marginBottom: 16 },
  linkingActions: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 16 },
  linkButton: { backgroundColor: '#3498db', padding: 12, borderRadius: 6, flex: 0.48 },
  linkButtonText: { color: '#fff', fontSize: 14, fontWeight: 'bold', textAlign: 'center' },
  unlinkButton: { backgroundColor: '#e74c3c', padding: 12, borderRadius: 6, flex: 0.48 },
  unlinkButtonText: { color: '#fff', fontSize: 14, fontWeight: 'bold', textAlign: 'center' },
  availableGroupsTitle: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50', marginBottom: 8 },
  groupsList: { flexDirection: 'row', flexWrap: 'wrap' },
  groupOption: { backgroundColor: '#ecf0f1', padding: 8, borderRadius: 16, marginRight: 8, marginBottom: 8 },
  selectedGroup: { backgroundColor: '#3498db' },
  groupOptionText: { fontSize: 12, color: '#555' },
  selectedGroupText: { color: '#fff', fontWeight: 'bold' },
});