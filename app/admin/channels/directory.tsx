import { <PERSON> } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function ChannelDirectory() {
  const channels = [
    {
      id: 'ch001',
      name: 'Tech News Daily',
      type: 'Public',
      subscribers: 125000,
      monetization: 'Enabled',
      revenue: '€5,000/mo',
      linkedGroup: 'Tech Enthusiasts',
    },
    {
      id: 'ch002',
      name: 'Exclusive Cooking Club',
      type: 'Private',
      subscribers: 500,
      monetization: 'Enabled',
      revenue: '€1,000/mo',
      linkedGroup: 'Cooking Masters',
    },
    {
      id: 'ch003',
      name: 'Secret Gaming Channel',
      type: 'Secret',
      subscribers: 50,
      monetization: 'Disabled',
      revenue: 'N/A',
      linkedGroup: 'None',
    },
    {
      id: 'ch004',
      name: 'Banned Channel',
      type: 'Public',
      subscribers: 0,
      monetization: 'Disabled',
      revenue: 'N/A',
      linkedGroup: 'None',
    },
  ];

  const searchFilters = [
    { name: 'Type', options: ['Public', 'Private', 'Secret'] },
    { name: 'Status', options: ['Active', 'Flagged', 'Banned'] },
    { name: 'Monetization', options: ['Enabled', 'Disabled', 'Pending'] },
    { name: 'Subscribers', options: ['Any', '<1k', '1k-10k', '>10k'] },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Channel Directory</Text>
        <Text style={styles.subtitle}>Searchable Database of All Channels</Text>
        <Link href="/admin/channels/management-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a searchable database of all channels on the platform, with advanced
          filtering and monetization indicators to help moderators manage content and revenue.
        </Text>

        <View style={styles.searchBox}>
          <Text style={styles.searchPlaceholder}>Search by channel name or ID...</Text>
        </View>

        <Text style={styles.sectionTitle}>Search Filters:</Text>
        <View style={styles.filtersContainer}>
          {searchFilters.map((filter, index) => (
            <TouchableOpacity key={index} style={styles.filterButton}>
              <Text style={styles.filterText}>{filter.name}: All</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Channel Directory:</Text>
        {channels.map((channel) => (
          <Link key={channel.id} href={`/admin/channels/${channel.id}`} asChild>
            <TouchableOpacity style={styles.channelCard}>
              <View style={styles.channelHeader}>
                <Text style={styles.channelName}>{channel.name}</Text>
                <Text style={styles.channelType}>{channel.type}</Text>
              </View>
              <Text style={styles.channelDetails}>Subscribers: {channel.subscribers}</Text>
              <View style={styles.channelMeta}>
                <Text style={styles.metaText}>Monetization: {channel.monetization}</Text>
                <Text style={styles.revenueText}>Revenue: {channel.revenue}</Text>
              </View>
              <View style={styles.linkedGroupContainer}>
                <Text style={styles.linkedGroupLabel}>Linked Group: </Text>
                <Text style={[styles.linkedGroupValue, {
                  color: channel.linkedGroup === 'None' ? '#95a5a6' : '#3498db'
                }]}>
                  {channel.linkedGroup}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Advanced channel search and filtering</Text>
          <Text style={styles.feature}>• Monetization status indicators</Text>
          <Text style={styles.feature}>• Subscriber count tracking</Text>
          <Text style={styles.feature}>• Content performance monitoring</Text>
          <Text style={styles.feature}>• Revenue analytics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Channel Selection → Channel Detail Screen with monetization tools
          {
            '\n'}
          • Monetization Review → Revenue analysis interface with detailed metrics
          {
            '\n'}
          • Content Moderation → Channel content review and moderation actions
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e67e22', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  searchBox: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 16, marginBottom: 16, backgroundColor: '#fff' },
  searchPlaceholder: { color: '#999', fontSize: 14 },
  filtersContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  filterButton: { backgroundColor: '#fff', padding: 10, borderRadius: 20, marginRight: 8, marginBottom: 8 },
  filterText: { fontSize: 12, color: '#555' },
  channelCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  channelHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  channelName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  channelType: { fontSize: 14, color: '#e67e22', fontWeight: 'bold' },
  channelDetails: { fontSize: 12, color: '#555', marginBottom: 8 },
  channelMeta: { flexDirection: 'row', justifyContent: 'space-between' },
  metaText: { fontSize: 12, color: '#7f8c8d' },
  revenueText: { fontSize: 12, fontWeight: 'bold', color: '#27ae60' },
  linkedGroupContainer: { flexDirection: 'row', alignItems: 'center', marginTop: 8 },
  linkedGroupLabel: { fontSize: 12, color: '#7f8c8d' },
  linkedGroupValue: { fontSize: 12, fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});