import { Stack } from 'expo-router';

export default function ChannelsLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Channel Management',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="management-dashboard" 
        options={{ 
          title: 'Channel Management Dashboard',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="directory" 
        options={{ 
          title: 'Channel Directory',
          headerShown: false 
        }} 
      />
      <Stack.Screen
        name="[id]"
        options={{
          title: 'Channel Detail',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="creation-requests"
        options={{
          title: 'Channel Creation Requests',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="banned-channels"
        options={{
          title: 'Banned Channels',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="secret-monitoring"
        options={{
          title: 'Secret Channel Monitoring',
          headerShown: false
        }}
      />
    </Stack>
  );
}
