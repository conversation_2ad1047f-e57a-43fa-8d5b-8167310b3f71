import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AutomaticDetection() {
  const detectedContent = [
    {
      id: 'ad001',
      type: 'Image',
      category: 'Violence',
      confidence: 0.98,
      date: '2024-01-18',
    },
    {
      id: 'ad002',
      type: 'Text',
      category: 'Hate Speech',
      confidence: 0.91,
      date: '2024-01-18',
    },
    {
      id: 'ad003',
      type: 'Video',
      category: 'Sexual Content',
      confidence: 0.85,
      date: '2024-01-17',
    },
    {
      id: 'ad004',
      type: 'Text',
      category: 'Spam',
      confidence: 0.72,
      date: '2024-01-16',
    },
  ];

  const confidenceCategories = [
    { name: 'High Confidence (90%+)', description: 'Likely violations requiring review' },
    { name: 'Medium Confidence (70-89%)', description: 'Potential violations for verification' },
    { name: 'Low Confidence (50-69%)', description: 'Flagged for human assessment' },
    { name: 'False Positives', description: 'Incorrectly flagged content' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Automatic Detection Alerts</Text>
        <Text style={styles.subtitle}>Review AI-Detected Content</Text>
        <Link href="/admin/content/moderation-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen displays content automatically flagged by the AI detection system. It allows
          moderators to review items based on confidence levels, report false positives, and
          escalate critical cases.
        </Text>

        <Text style={styles.sectionTitle}>AI-Detected Content Queue:</Text>
        {detectedContent.map((item) => (
          <Link key={item.id} href={`/admin/content/${item.id}`} asChild>
            <TouchableOpacity style={styles.detectionCard}>
              <View style={styles.detectionHeader}>
                <Text style={styles.detectionType}>{item.type}</Text>
                <Text style={styles.detectionCategory}>{item.category}</Text>
              </View>
              <Text style={styles.detectionDetails}>Confidence: {item.confidence}</Text>
              <Text style={styles.detectionDetails}>Date: {item.date}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>AI Detection Categories:</Text>
        <View style={styles.categoriesContainer}>
          {confidenceCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• AI detection results display</Text>
          <Text style={styles.feature}>• Confidence level indicators</Text>
          <Text style={styles.feature}>• False positive reporting</Text>
          <Text style={styles.feature}>• Pattern recognition insights</Text>
          <Text style={styles.feature}>• Detection accuracy metrics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Content Review → Manual review interface for detailed analysis
          {
            '\n'}
          • False Positive → AI training feedback system to improve accuracy
          {
            '\n'}
          • Escalation → Senior moderator review for critical cases
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f39c12', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  detectionCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  detectionHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  detectionType: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  detectionCategory: { fontSize: 14, color: '#f39c12', fontWeight: 'bold' },
  detectionDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});