import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ManualReview() {
  const reviewQueue = [
    {
      id: 'mr001',
      type: 'Video',
      priority: 'Critical',
      reason: 'Potential terrorism content',
      assignedTo: 'SeniorMod01',
      timeInQueue: '5m',
    },
    {
      id: 'mr002',
      type: 'Image',
      priority: 'High',
      reason: 'Severe hate speech',
      assignedTo: 'JuniorMod03',
      timeInQueue: '15m',
    },
    {
      id: 'mr003',
      type: 'Text',
      priority: 'Medium',
      reason: 'Harassment complaint',
      assignedTo: 'JuniorMod02',
      timeInQueue: '45m',
    },
    {
      id: 'mr004',
      type: 'Link',
      priority: 'Low',
      reason: 'Spam',
      assignedTo: '-',
      timeInQueue: '2h',
    },
  ];

  const priorityLevels = [
    { name: 'Critical', description: 'Terrorism, violence, illegal content' },
    { name: 'High', description: 'Hate speech, harassment, threats' },
    { name: 'Medium', description: 'Spam, inappropriate content' },
    { name: 'Low', description: 'Minor policy violations' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Manual Review Queue</Text>
        <Text style={styles.subtitle}>Content Requiring Human Review</Text>
        <Link href="/admin/content/moderation-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages content that requires human review, organized by priority. It includes
          tools for assigning reviews, tracking time, and ensuring quality assurance.
        </Text>

        <Text style={styles.sectionTitle}>Manual Review Queue:</Text>
        {reviewQueue.map((item) => (
          <Link key={item.id} href={`/admin/content/${item.id}`} asChild>
            <TouchableOpacity style={styles.reviewCard}>
              <View style={styles.reviewHeader}>
                <Text style={styles.reviewType}>{item.type}</Text>
                <Text style={[styles.priority, { color: item.priority === 'Critical' ? '#8e44ad' : item.priority === 'High' ? '#e74c3c' : '#f39c12' }]}>
                  {item.priority}
                </Text>
              </View>
              <Text style={styles.reviewReason}>{item.reason}</Text>
              <Text style={styles.reviewDetails}>Assigned to: {item.assignedTo} | In queue: {item.timeInQueue}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Priority Levels:</Text>
        <View style={styles.priorityContainer}>
          {priorityLevels.map((level, index) => (
            <View key={index} style={styles.priorityCard}>
              <Text style={styles.priorityName}>{level.name}</Text>
              <Text style={styles.priorityDescription}>{level.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Priority-based queue management</Text>
          <Text style={styles.feature}>• Escalation procedures</Text>
          <Text style={styles.feature}>• Review assignment system</Text>
          <Text style={styles.feature}>• Time tracking for reviews</Text>
          <Text style={styles.feature}>• Quality assurance metrics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Content Assignment → Assigned review interface with timer
          {
            "\n" 
          }
          • Escalation → Senior moderator involvement for critical cases
          {
            "\n" 
          }
          • Policy Consultation → Access to policy reference materials
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e67e22', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  reviewCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  reviewHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  reviewType: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  priority: { fontSize: 14, fontWeight: 'bold' },
  reviewReason: { fontSize: 14, color: '#555', marginBottom: 8 },
  reviewDetails: { fontSize: 12, color: '#7f8c8d' },
  priorityContainer: { marginBottom: 16 },
  priorityCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  priorityName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  priorityDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});