import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ReportedContent() {
  const reportedContent = [
    {
      id: 'c001',
      type: 'Image',
      category: 'Hate Speech',
      aiScore: 0.95,
      reporter: 'user015',
      author: 'user016',
      date: '2024-01-18',
    },
    {
      id: 'c002',
      type: 'Text',
      category: 'Spam',
      aiScore: 0.78,
      reporter: 'user017',
      author: 'user018',
      date: '2024-01-17',
    },
    {
      id: 'c003',
      type: 'Video',
      category: 'Violence',
      aiScore: 0.99,
      reporter: 'user019',
      author: 'user020',
      date: '2024-01-16',
    },
  ];

  const reviewProcess = [
    { step: 1, name: 'Content Display', description: 'Full content with context' },
    { step: 2, name: 'AI Analysis', description: 'Confidence scores and categories' },
    { step: 3, name: 'Reporter Details', description: 'User who submitted report' },
    { step: 4, name: 'Moderation Decision', description: 'Approve, remove, or escalate' },
    { step: 5, name: 'Action Logging', description: 'Complete audit trail' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Reported Content</Text>
        <Text style={styles.subtitle}>Review User-Reported Content</Text>
        <Link href="/admin/content/moderation-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for reviewing content that has been reported by users. It includes AI
          confidence scores and various tools to help moderators make informed decisions.
        </Text>

        <Text style={styles.sectionTitle}>Reported Content Queue:</Text>
        {reportedContent.map((item) => (
          <Link key={item.id} href={`/admin/content/${item.id}`} asChild>
            <TouchableOpacity style={styles.reportCard}>
              <View style={styles.reportHeader}>
                <Text style={styles.reportType}>{item.type}</Text>
                <Text style={styles.reportCategory}>{item.category}</Text>
              </View>
              <Text style={styles.reportDetails}>AI Score: {item.aiScore}</Text>
              <Text style={styles.reportDetails}>Reported by: {item.reporter} | Author: {item.author}</Text>
              <Text style={styles.reportDetails}>Date: {item.date}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Review Process:</Text>
        <View style={styles.processContainer}>
          {reviewProcess.map((item) => (
            <View key={item.step} style={styles.processStep}>
              <Text style={styles.stepNumber}>{item.step}</Text>
              <View style={styles.stepInfo}>
                <Text style={styles.stepName}>{item.name}</Text>
                <Text style={styles.stepDescription}>{item.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Content review interface</Text>
          <Text style={styles.feature}>• AI confidence scoring</Text>
          <Text style={styles.feature}>• Category-based filtering</Text>
          <Text style={styles.feature}>• Batch processing tools</Text>
          <Text style={styles.feature}>• Reporter information display</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Content Detail → Detailed content analysis and moderation actions
          {
            '\n'}
          • User Profile → View reporter and author profiles
          {
            '\n'}
          • Policy Reference → Access relevant policy guidelines
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  reportCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  reportHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  reportType: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  reportCategory: { fontSize: 14, color: '#e74c3c', fontWeight: 'bold' },
  reportDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  processContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  processStep: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  stepNumber: { fontSize: 18, fontWeight: 'bold', color: '#e74c3c', marginRight: 12 },
  stepInfo: { flex: 1 },
  stepName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  stepDescription: { fontSize: 12, color: '#555' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});