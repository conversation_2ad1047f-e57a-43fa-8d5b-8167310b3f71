import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function StoriesManagement() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Stories Management System</Text>
        <Text style={styles.subtitle}>Centralized moderation and review of user Stories content</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>System Purpose:</Text>
        <Text style={styles.description}>
          Centralized moderation and review of user Stories content, including text, media, 
          music, location tags, and privacy settings. Stories must be included in both 
          automated and manual moderation workflows based on privacy type.
        </Text>

        <Text style={styles.sectionTitle}>Stories Moderation Dashboard Components:</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Filtered list of reported Stories</Text>
          <Text style={styles.component}>• AI-flagged Stories (for public/private groups/channels)</Text>
          <Text style={styles.component}>• Manual review queue (for secret groups/channels)</Text>
          <Text style={styles.component}>• Privacy indicators (Private Profile / Public Page / Both)</Text>
          <Text style={styles.component}>• Location and music metadata</Text>
          <Text style={styles.component}>• AI confidence score for prohibited content</Text>
        </View>

        <Text style={styles.sectionTitle}>Story Detail Screen Components:</Text>
        <View style={styles.detailComponents}>
          <Text style={styles.component}>• Full Story preview (text, image, video, music overlay)</Text>
          <Text style={styles.component}>• Creator information (PID, ID, verification status)</Text>
          <Text style={styles.component}>• Location tag (city, neighborhood, venue)</Text>
          <Text style={styles.component}>• Music metadata (track name, artist if available)</Text>
          <Text style={styles.component}>• Reporting details and history</Text>
          <Text style={styles.component}>• AI analysis summary</Text>
          <Text style={styles.component}>• Privacy setting (Private, Public, Both)</Text>
        </View>

        <Text style={styles.sectionTitle}>Moderation Workflow:</Text>
        <View style={styles.workflowSteps}>
          <Text style={styles.step}>1. Story content detection and flagging</Text>
          <Text style={styles.step}>2. Automatic moderation for public/private content</Text>
          <Text style={styles.step}>3. Manual review queue for secret content</Text>
          <Text style={styles.step}>4. Content action decisions (warn, remove, ban)</Text>
          <Text style={styles.step}>5. User notification and appeal process</Text>
        </View>

        <Text style={styles.sectionTitle}>Privacy-Based Moderation:</Text>
        <View style={styles.privacyTypes}>
          <View style={styles.privacyItem}>
            <View style={[styles.privacyIndicator, { backgroundColor: '#27ae60' }]} />
            <Text style={styles.privacyText}>Public Stories: Automatic AI moderation</Text>
          </View>
          <View style={styles.privacyItem}>
            <View style={[styles.privacyIndicator, { backgroundColor: '#f39c12' }]} />
            <Text style={styles.privacyText}>Private Stories: Automatic AI moderation</Text>
          </View>
          <View style={styles.privacyItem}>
            <View style={[styles.privacyIndicator, { backgroundColor: '#e74c3c' }]} />
            <Text style={styles.privacyText}>Secret Stories: Manual review required</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Story selection → Story Detail Screen</Text>
          <Text style={styles.navItem}>• AI alerts → Automatic Detection Alerts Screen</Text>
          <Text style={styles.navItem}>• Manual review → Manual Review Queue Screen</Text>
          <Text style={styles.navItem}>• Content action → Content Action Screen</Text>
          <Text style={styles.navItem}>• User details → User Detail Screen</Text>
          <Text style={styles.navItem}>• Report history → Reported Content Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Story-Specific Moderation Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• Location tag verification and filtering</Text>
          <Text style={styles.feature}>• Music overlay content analysis</Text>
          <Text style={styles.feature}>• Background image/video screening</Text>
          <Text style={styles.feature}>• Text overlay content detection</Text>
          <Text style={styles.feature}>• Duration-based content analysis</Text>
          <Text style={styles.feature}>• Cross-platform story sharing monitoring</Text>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Review Flagged</Text>
            <Text style={styles.actionDescription}>AI-flagged Stories requiring review</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Manual Queue</Text>
            <Text style={styles.actionDescription}>Secret Stories for manual review</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Reported Stories</Text>
            <Text style={styles.actionDescription}>User-reported Story content</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Location Filter</Text>
            <Text style={styles.actionDescription}>Filter by location tags</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#9b59b6',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  detailComponents: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  workflowSteps: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  step: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  privacyTypes: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  privacyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  privacyIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  privacyText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#9b59b6',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#9b59b6',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
});
