import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function PolicyReference() {
  const policyCategories = [
    { 
      name: 'Community Rules', 
      description: 'Behavior guidelines and user conduct' 
    }, { 
      name: 'Content Standards', 
      description: 'Acceptable content types and restrictions' 
    }, { 
      name: 'Legal Compliance', 
      description: 'Regulatory requirements and legal issues' 
    }, { 
      name: 'Enforcement Procedures', 
      description: 'Moderation protocols and penalties' 
    },
  ];

  const recentUpdates = [
    { 
      title: 'Update to Hate Speech Policy', 
      date: '2024-01-15', 
      summary: 'Clarified definitions and examples.' 
    }, { 
      title: 'New Policy on AI-Generated Content', 
      date: '2023-12-20', 
      summary: 'Guidelines for disclosure and watermarking.' 
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Policy Reference</Text>
        <Text style={styles.subtitle}>Searchable Policy Database</Text>
        <Link href="/admin/content/moderation-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a searchable database of all platform policies, including violation
          examples, moderation guidelines, and decision precedents to ensure consistent enforcement.
        </Text>

        <View style={styles.searchBox}>
          <Text style={styles.searchPlaceholder}>Search policies, examples, or guidelines...</Text>
        </View>

        <Text style={styles.sectionTitle}>Policy Categories:</Text>
        <View style={styles.categoriesContainer}>
          {policyCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Recent Updates:</Text>
        <View style={styles.updatesContainer}>
          {recentUpdates.map((update, index) => (
            <View key={index} style={styles.updateCard}>
              <Text style={styles.updateTitle}>{update.title}</Text>
              <Text style={styles.updateDate}>{update.date}</Text>
              <Text style={styles.updateSummary}>{update.summary}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Comprehensive policy search</Text>
          <Text style={styles.feature}>• Violation examples library</Text>
          <Text style={styles.feature}>• Moderation guidelines</Text>
          <Text style={styles.feature}>• Decision precedents</Text>
          <Text style={styles.feature}>• Policy update notifications</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Policy Details → Specific policy documents with full text
          
          • Examples → Violation example library with case studies
          
          • Updates → Policy change notifications and version history
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  searchBox: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 16, marginBottom: 16, backgroundColor: '#fff' },
  searchPlaceholder: { color: '#999', fontSize: 14 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#3498db' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  updatesContainer: { marginBottom: 16 },
  updateCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  updateTitle: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  updateDate: { fontSize: 12, color: '#7f8c8d', marginVertical: 4 },
  updateSummary: { fontSize: 12, color: '#555' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});