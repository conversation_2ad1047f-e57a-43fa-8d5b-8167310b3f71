import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';

export default function ContentDetail() {
  const { id } = useLocalSearchParams();

  const contentData = {
    id: id as string,
    type: 'Image',
    category: 'Hate Speech',
    aiScore: 0.95,
    reporter: 'user015',
    author: 'user016',
    date: '2024-01-18',
    contentUrl: 'https://via.placeholder.com/400x250.png?text=Reported+Image',
    context: 'Posted in group \'Public Square\'.'
  };

  const moderationActions = [
    { name: 'Approve', color: '#27ae60' },
    { name: 'Remove', color: '#e74c3c' },
    { name: 'Warn User', color: '#f39c12' },
    { name: 'Escalate', color: '#8e44ad' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Content Detail</Text>
        <Text style={styles.subtitle}>Content ID: {contentData.id}</Text>
        <Link href="/admin/content/reported" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Reports</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a detailed analysis of a specific piece of reported content,
          offering various moderation tools and contextual information to make an informed decision.
        </Text>

        <Text style={styles.sectionTitle}>Content Display:</Text>
        <View style={styles.contentCard}>
          <Image source={{ uri: contentData.contentUrl }} style={styles.contentImage} />
          <Text style={styles.contentContext}>{contentData.context}</Text>
        </View>

        <Text style={styles.sectionTitle}>Content Analysis:</Text>
        <View style={styles.analysisCard}>
          <Text style={styles.analysisText}>Type: {contentData.type}</Text>
          <Text style={styles.analysisText}>Category: {contentData.category}</Text>
          <Text style={styles.analysisText}>AI Confidence: {contentData.aiScore}</Text>
          <Text style={styles.analysisText}>Author's Violation History: 3 previous violations</Text>
          <Text style={styles.analysisText}>Community Impact: 1.2k views, 45 comments</Text>
        </View>

        <Text style={styles.sectionTitle}>Moderation Actions:</Text>
        <View style={styles.actionsContainer}>
          {moderationActions.map((action, index) => (
            <TouchableOpacity key={index} style={[styles.actionButton, { backgroundColor: action.color }]}>
              <Text style={styles.actionButtonText}>{action.name}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
            <Link href={`/admin/users/${contentData.author}`} asChild>
                <TouchableOpacity style={styles.navButton}>
                    <Text style={styles.navButtonText}>View Author's Profile</Text>
                </TouchableOpacity>
            </Link>
            <Link href={`/admin/groups/1`} asChild>
                <TouchableOpacity style={styles.navButton}>
                    <Text style={styles.navButtonText}>View Group/Channel</Text>
                </TouchableOpacity>
            </Link>
        </View>

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#c0392b', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  contentCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, alignItems: 'center' },
  contentImage: { width: '100%', height: 200, borderRadius: 8, marginBottom: 12, backgroundColor: '#ecf0f1' },
  contentContext: { fontSize: 14, color: '#555', fontStyle: 'italic' },
  analysisCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginTop: 16 },
  analysisText: { fontSize: 14, color: '#555', marginBottom: 8 },
  actionsContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginTop: 16 },
  actionButton: { paddingVertical: 12, paddingHorizontal: 16, borderRadius: 6, width: '48%', alignItems: 'center', marginBottom: 8 },
  actionButtonText: { color: '#fff', fontWeight: 'bold' },
  navigationSection: { marginTop: 24 },
  navButton: { backgroundColor: '#95a5a6', padding: 12, borderRadius: 6, alignItems: 'center', marginBottom: 8 },
  navButtonText: { color: '#fff', fontSize: 14 },
});