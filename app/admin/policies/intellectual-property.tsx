import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function IntellectualProperty() {
  const ipCategories = [
    { name: 'Copyright Protection', description: 'Original content protection' },
    { name: 'Trademark Rights', description: 'Brand and logo protection' },
    { name: 'Fair Use Guidelines', description: 'Acceptable use of copyrighted material' },
    { name: 'DMCA Procedures', description: 'Takedown and counter-notice processes' },
  ];

  const violationReports = [
    { id: 'ipr001', type: 'Copyright', status: 'Pending Review' },
    { id: 'ipr002', type: 'Trademark', status: 'Action Taken' },
    { id: 'ipr003', type: 'Copyright', status: 'Pending Review' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Intellectual Property</Text>
        <Text style={styles.subtitle}>Copyright and Trademark Protection Guidelines</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages policies related to intellectual property, including copyright and
          trademark protection, DMCA takedown procedures, and fair use guidelines.
        </Text>

        <Text style={styles.sectionTitle}>IP Categories:</Text>
        <View style={styles.categoriesContainer}>
          {ipCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Violation Reports:</Text>
        <View style={styles.reportsContainer}>
          {violationReports.map((report, index) => (
            <View key={index} style={styles.reportItem}>
              <Text style={styles.reportId}>Report ID: {report.id}</Text>
              <Text style={styles.reportType}>Type: {report.type}</Text>
              <Text style={styles.reportStatus}>Status: {report.status}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• IP policy management</Text>
          <Text style={styles.feature}>• Copyright violation tracking</Text>
          <Text style={styles.feature}>• DMCA takedown procedures</Text>
          <Text style={styles.feature}>• Trademark protection protocols</Text>
          <Text style={styles.feature}>• Fair use guidelines</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Policy Management → IP policy editing interface
          
          • Violation Reports → IP violation tracking and case management
          
          • DMCA Process → Takedown request management and counter-notice workflow
          
          • Fair Use Guide → Fair use determination tools and guidelines
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#9b59b6' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  reportsContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  reportItem: { paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  reportId: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  reportType: { fontSize: 12, color: '#555', marginTop: 4 },
  reportStatus: { fontSize: 12, color: '#e74c3c', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});
