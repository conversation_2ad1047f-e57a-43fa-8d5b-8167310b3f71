import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function TermsOfUse() {
  const termsCategories = [
    { name: 'Service Agreement', description: 'Platform usage terms' },
    { name: 'User Responsibilities', description: 'Account and content obligations' },
    { name: 'Platform Rights', description: 'Service provider rights and limitations' },
    { name: 'Dispute Resolution', description: 'Conflict resolution procedures' },
  ];

  const versionHistory = [
    { version: '3.1', date: '2024-01-15', author: 'Legal Team' },
    { version: '3.0', date: '2023-11-20', author: 'Legal Team' },
    { version: '2.5', date: '2023-08-01', author: 'Legal Team' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Terms of Use</Text>
        <Text style={styles.subtitle}>Legal Terms and Conditions Management</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing the legal terms and conditions of the platform. It includes
          a legal document editor, version control, and user acceptance tracking.
        </Text>

        <Text style={styles.sectionTitle}>Terms Categories:</Text>
        <View style={styles.categoriesContainer}>
          {termsCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Version History:</Text>
        <View style={styles.historyContainer}>
          {versionHistory.map((item, index) => (
            <View key={index} style={styles.historyItem}>
              <Text style={styles.historyVersion}>Version {item.version}</Text>
              <Text style={styles.historyDetails}>Date: {item.date} | Author: {item.author}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Legal document editor</Text>
          <Text style={styles.feature}>• Version control system</Text>
          <Text style={styles.feature}>• User acceptance tracking</Text>
          <Text style={styles.feature}>• Legal compliance verification</Text>
          <Text style={styles.feature}>• Multi-language support</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Document Editor → Legal document editing interface with change tracking
          
          • Version History → Terms version tracking and comparison tools
          
          • User Acceptance → Acceptance rate analytics and reporting
          
          • Legal Review → Legal compliance verification and approval workflow
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#3498db' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  historyContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  historyItem: { paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  historyVersion: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  historyDetails: { fontSize: 12, color: '#7f8c8d', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});
