import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function PolicyImplementation() {
  const implementationFeatures = [
    { name: 'Automated Enforcement', description: 'AI-powered policy enforcement' },
    { name: 'Manual Review', description: 'Human oversight for complex cases' },
    { name: 'Compliance Monitoring', description: 'Real-time policy adherence tracking' },
    { name: 'Effectiveness Analysis', description: 'Policy impact measurement' },
  ];

  const enforcementMetrics = [
    { label: 'Compliance Rate', value: '97.8%', color: '#27ae60' },
    { label: 'Violations (30d)', value: '380', color: '#e74c3c' },
    { label: 'Enforcement Actions', value: '156', color: '#f39c12' },
    { label: 'Appeal Success Rate', value: '23%', color: '#3498db' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Policy Implementation</Text>
        <Text style={styles.subtitle}>Policy Enforcement and Compliance Monitoring</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for monitoring the enforcement and compliance of platform policies. It
          includes tools for tracking implementation status, enforcement metrics, and effectiveness.
        </Text>

        <Text style={styles.sectionTitle}>Enforcement Metrics:</Text>
        <View style={styles.metricsGrid}>
          {enforcementMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Implementation Features:</Text>
        <View style={styles.featuresContainer}>
          {implementationFeatures.map((feature, index) => (
            <TouchableOpacity key={index} style={styles.featureCard}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Implementation status tracking</Text>
          <Text style={styles.feature}>• Enforcement metrics</Text>
          <Text style={styles.feature}>• Compliance reporting</Text>
          <Text style={styles.feature}>• Violation pattern analysis</Text>
          <Text style={styles.feature}>• Effectiveness measurement</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Enforcement Tools → Automated enforcement configuration and rules
          {
            '\n'}
          • Compliance Reports → Policy adherence analytics and data exports
          {
            '\n'}
          • Violation Analysis → Pattern recognition and trend analysis tools
          {
            '\n'}
          • Effectiveness Review → Policy impact assessment and reporting
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#1abc9c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, borderTopWidth: 4, alignItems: 'center' },
  metricValue: { fontSize: 22, fontWeight: 'bold', color: '#2c3e50' },
  metricLabel: { fontSize: 12, color: '#7f8c8d', textAlign: 'center', marginTop: 4 },
  featuresContainer: { marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  featureName: { fontSize: 16, fontWeight: 'bold', color: '#1abc9c' },
  featureDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});