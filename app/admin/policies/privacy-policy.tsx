import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function PrivacyPolicy() {
  const privacyCategories = [
    { name: 'Data Collection', description: 'Information gathering practices' },
    { name: 'Data Usage', description: 'How user data is utilized' },
    { name: 'Data Protection', description: 'Security and privacy measures' },
    { name: 'User Rights', description: 'Data access and deletion rights' },
  ];

  const complianceChecks = [
    { name: 'GDPR', status: 'Compliant' },
    { name: 'CCPA', status: 'Compliant' },
    { name: 'COPPA', status: 'Compliant' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Privacy Policy</Text>
        <Text style={styles.subtitle}>Data Collection, Usage, and Protection Policies</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing the platform&apos;s privacy policy, including data collection,
          usage, protection, and user rights. It also tracks international compliance.
        </Text>

        <Text style={styles.sectionTitle}>Privacy Categories:</Text>
        <View style={styles.categoriesContainer}>
          {privacyCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Compliance Status:</Text>
        <View style={styles.complianceContainer}>
          {complianceChecks.map((item, index) => (
            <View key={index} style={styles.complianceItem}>
              <Text style={styles.complianceName}>{item.name}</Text>
              <Text style={styles.complianceStatus}>{item.status}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Privacy policy editor</Text>
          <Text style={styles.feature}>• Data protection compliance</Text>
          <Text style={styles.feature}>• User consent management</Text>
          <Text style={styles.feature}>• Data retention policies</Text>
          <Text style={styles.feature}>• International compliance (GDPR, CCPA)</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Policy Editor → Privacy policy editing interface with version control
          
          • Compliance Check → Regulatory compliance verification and reporting
          
          • User Consent → Consent management system with audit trail
          
          • Data Rights → User data rights management and request processing
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#27ae60', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#27ae60' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  complianceContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  complianceItem: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  complianceName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  complianceStatus: { fontSize: 14, color: '#27ae60', fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});
