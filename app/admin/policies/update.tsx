import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function PolicyUpdate() {
  const updateProcess = [
    { step: 1, name: 'Draft Creation', description: 'Policy modification drafting' },
    { step: 2, name: 'Legal Review', description: 'Legal team approval process' },
    { step: 3, name: 'Stakeholder Review', description: 'Internal review and feedback' },
    { step: 4, name: 'User Notification', description: 'Policy change announcements' },
    { step: 5, name: 'Implementation', description: 'Policy activation and enforcement' },
  ];

  const pendingUpdates = [
    { id: 'pu001', policy: 'Community Rules', status: 'Legal Review' },
    { id: 'pu002', policy: 'Privacy Policy', status: 'Draft Creation' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Policy Update</Text>
        <Text style={styles.subtitle}>Policy Modification and Version Control</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages the policy modification and version control system, including a
          structured approval workflow and user notification system.
        </Text>

        <Text style={styles.sectionTitle}>Update Process:</Text>
        <View style={styles.processContainer}>
          {updateProcess.map((item) => (
            <View key={item.step} style={styles.processStep}>
              <Text style={styles.stepNumber}>{item.step}</Text>
              <View style={styles.stepInfo}>
                <Text style={styles.stepName}>{item.name}</Text>
                <Text style={styles.stepDescription}>{item.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Pending Updates:</Text>
        <View style={styles.updatesContainer}>
          {pendingUpdates.map((update, index) => (
            <View key={index} style={styles.updateItem}>
              <Text style={styles.updatePolicy}>{update.policy}</Text>
              <Text style={styles.updateStatus}>{update.status}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Policy editing interface</Text>
          <Text style={styles.feature}>• Change tracking system</Text>
          <Text style={styles.feature}>• Approval workflow</Text>
          <Text style={styles.feature}>• User notification system</Text>
          <Text style={styles.feature}>• Implementation scheduling</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Draft Editor → Policy modification interface with rich text editor
          {
            '\n'}
          • Review Process → Approval workflow management with commenting
          {
            '\n'}
          • Notification System → User communication tools for announcements
          {
            '\n'}
          • Implementation → Policy activation controls and scheduling
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#1abc9c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  processContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  processStep: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  stepNumber: { fontSize: 18, fontWeight: 'bold', color: '#1abc9c', marginRight: 12 },
  stepInfo: { flex: 1 },
  stepName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  stepDescription: { fontSize: 12, color: '#555' },
  updatesContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  updateItem: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  updatePolicy: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  updateStatus: { fontSize: 14, color: '#f39c12', fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});