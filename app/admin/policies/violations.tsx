import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ViolationExamples() {
  const exampleCategories = [
    { name: 'Clear Violations', description: 'Obvious policy breaches' },
    { name: 'Borderline Cases', description: 'Difficult moderation decisions' },
    { name: 'Context-Dependent', description: 'Situational violation examples' },
    { name: 'False Positives', description: 'Incorrectly flagged content' },
  ];

  const caseStudies = [
    { id: 'cs001', title: 'Case Study: Hate Speech vs. Satire', category: 'Borderline Cases' },
    { id: 'cs002', title: 'Case Study: Spam in Private Groups', category: 'Clear Violations' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Violation Examples</Text>
        <Text style={styles.subtitle}>Violation Examples and Moderation Guidelines</Text>
        <Link href="/admin/policies/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Policies</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a comprehensive library of violation examples, moderation
          guidelines, and case studies to ensure consistent decision-making.
        </Text>

        <Text style={styles.sectionTitle}>Example Categories:</Text>
        <View style={styles.categoriesContainer}>
          {exampleCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Case Studies:</Text>
        <View style={styles.caseStudiesContainer}>
          {caseStudies.map((study, index) => (
            <View key={index} style={styles.caseStudyItem}>
              <Text style={styles.caseStudyTitle}>{study.title}</Text>
              <Text style={styles.caseStudyCategory}>{study.category}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Violation example library</Text>
          <Text style={styles.feature}>• Moderation decision precedents</Text>
          <Text style={styles.feature}>• Training materials for moderators</Text>
          <Text style={styles.feature}>• Case study database</Text>
          <Text style={styles.feature}>• Decision consistency tools</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Example Library → Violation example database with search and filtering
          {
            '\n'}
          • Moderation Guide → Decision-making guidelines and best practices
          {
            '\n'}
          • Training Materials → Moderator education resources and quizzes
          {
            '\n'}
          • Case Studies → Detailed violation analysis and discussion
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#e74c3c' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  caseStudiesContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  caseStudyItem: { paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  caseStudyTitle: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  caseStudyCategory: { fontSize: 12, color: '#7f8c8d', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});