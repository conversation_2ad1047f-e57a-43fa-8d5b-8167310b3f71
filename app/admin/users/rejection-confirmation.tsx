import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RejectionConfirmation() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Rejection Confirmation</Text>
        <Text style={styles.subtitle}>Confirm verification rejection and refund processing</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Final confirmation screen for verification rejection. This screen confirms the 
          rejection reason, refund processing, and sends detailed feedback to the user 
          about why their verification was denied and how to improve.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Rejection summary</Text>
          <Text style={styles.component}>• Refund confirmation details</Text>
          <Text style={styles.component}>• User notification preview</Text>
          <Text style={styles.component}>• Improvement suggestions</Text>
          <Text style={styles.component}>• Final confirmation button</Text>
        </View>

        <Text style={styles.sectionTitle}>Rejection Summary:</Text>
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>User:</Text>
            <Text style={styles.summaryValue}>Jane Smith (PID67890)</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Verification Type:</Text>
            <Text style={styles.summaryValue}>Public Page Verification</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Payment Amount:</Text>
            <Text style={styles.summaryValue}>24,49€</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Rejection Reason:</Text>
            <Text style={styles.summaryValue}>Invalid Documentation</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Processing Date:</Text>
            <Text style={styles.summaryValue}>2024-01-15 14:30:00</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Refund Processing:</Text>
        <View style={styles.refundCard}>
          <View style={styles.refundHeader}>
            <Text style={styles.refundTitle}>Automatic Refund</Text>
            <Text style={styles.refundAmount}>24,49€</Text>
          </View>
          <View style={styles.refundDetails}>
            <Text style={styles.refundLabel}>Refund Method:</Text>
            <Text style={styles.refundValue}>Original Payment Method</Text>
          </View>
          <View style={styles.refundDetails}>
            <Text style={styles.refundLabel}>Processing Time:</Text>
            <Text style={styles.refundValue}>3-5 business days</Text>
          </View>
          <View style={styles.refundDetails}>
            <Text style={styles.refundLabel}>Refund Status:</Text>
            <Text style={styles.refundValue}>Will be processed upon confirmation</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>User Notification Preview:</Text>
        <View style={styles.notificationPreview}>
          <Text style={styles.notificationTitle}>Verification Request Update</Text>
          <Text style={styles.notificationMessage}>
            We've reviewed your verification request for Public Page Verification (24,49€).
            {'\n\n'}Unfortunately, we cannot approve your request at this time due to:
            {'\n\n'}• Invalid Documentation: Submitted documents are unclear, expired, or do not meet verification standards
            {'\n\n'}Your payment of 24,49€ will be refunded to your original payment method within 3-5 business days.
            {'\n\n'}You can resubmit your verification request once you have addressed the issues mentioned above.
          </Text>
        </View>

        <Text style={styles.sectionTitle}>Improvement Suggestions:</Text>
        <View style={styles.improvementSuggestions}>
          <Text style={styles.suggestionTitle}>How to improve your next submission:</Text>
          <View style={styles.suggestionList}>
            <Text style={styles.suggestion}>• Ensure all documents are clear and high-resolution</Text>
            <Text style={styles.suggestion}>• Verify that documents are not expired</Text>
            <Text style={styles.suggestion}>• Make sure all information matches exactly</Text>
            <Text style={styles.suggestion}>• Use official government-issued identification</Text>
            <Text style={styles.suggestion}>• For business verification, include valid business registration</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Confirmation → User notification sent and refund processed</Text>
          <Text style={styles.navItem}>• Refund tracking → Refund tracking system</Text>
          <Text style={styles.navItem}>• User support → Support ticket creation</Text>
          <Text style={styles.navItem}>• Back → Verification Rejection Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>System Actions:</Text>
        <View style={styles.systemActions}>
          <Text style={styles.systemAction}>• Process automatic refund of 24,49€</Text>
          <Text style={styles.systemAction}>• Send detailed rejection notification to user</Text>
          <Text style={styles.systemAction}>• Log rejection reason for analytics</Text>
          <Text style={styles.systemAction}>• Update verification request status to "Rejected"</Text>
          <Text style={styles.systemAction}>• Allow user to resubmit after 24 hours</Text>
        </View>

        <Text style={styles.sectionTitle}>Resubmission Policy:</Text>
        <View style={styles.resubmissionPolicy}>
          <Text style={styles.policyTitle}>User can resubmit verification request:</Text>
          <Text style={styles.policyDetail}>• After 24 hours cooling period</Text>
          <Text style={styles.policyDetail}>• With improved documentation</Text>
          <Text style={styles.policyDetail}>• New payment required for resubmission</Text>
          <Text style={styles.policyDetail}>• Maximum 3 attempts per 30-day period</Text>
        </View>

        <Text style={styles.sectionTitle}>Final Actions:</Text>
        <View style={styles.finalActions}>
          <TouchableOpacity style={styles.confirmButton}>
            <Text style={styles.confirmButtonText}>Confirm Rejection & Process Refund</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit Message</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  summaryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  refundCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#f39c12',
  },
  refundHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  refundTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f39c12',
  },
  refundAmount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  refundDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  refundLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  refundValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  notificationPreview: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  notificationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 12,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  improvementSuggestions: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  suggestionList: {
    marginLeft: 8,
  },
  suggestion: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  systemActions: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  systemAction: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  resubmissionPolicy: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  policyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  policyDetail: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  finalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmButton: {
    backgroundColor: '#e74c3c',
    padding: 16,
    borderRadius: 8,
    width: '50%',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  editButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '22%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
