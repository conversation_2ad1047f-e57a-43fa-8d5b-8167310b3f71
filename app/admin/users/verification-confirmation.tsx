import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function VerificationConfirmation() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verification Confirmation</Text>
        <Text style={styles.subtitle}>Confirm verification approval and badge assignment</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Final confirmation screen for verification approval. This screen confirms the 
          verification type, pricing, badge assignment, and sends notification to the user 
          about their successful verification.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Verification summary</Text>
          <Text style={styles.component}>• Badge assignment confirmation</Text>
          <Text style={styles.component}>• Payment confirmation details</Text>
          <Text style={styles.component}>• User notification preview</Text>
          <Text style={styles.component}>• Final confirmation button</Text>
        </View>

        <Text style={styles.sectionTitle}>Verification Summary:</Text>
        <View style={styles.summaryCard}>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>User:</Text>
            <Text style={styles.summaryValue}>John Doe (PID12345)</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Verification Type:</Text>
            <Text style={styles.summaryValue}>Both Verifications</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Payment Amount:</Text>
            <Text style={styles.summaryValue}>40,49€</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Account Type:</Text>
            <Text style={styles.summaryValue}>Business</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Processing Date:</Text>
            <Text style={styles.summaryValue}>2024-01-15 14:30:00</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Badge Assignment:</Text>
        <View style={styles.badgeAssignment}>
          <View style={styles.badgeCard}>
            <Text style={styles.badgeType}>Private Profile Badge</Text>
            <Text style={styles.badgeDescription}>✓ Identity verified for private profile</Text>
            <Text style={styles.badgeStatus}>Will be assigned</Text>
          </View>
          <View style={styles.badgeCard}>
            <Text style={styles.badgeType}>Public Page Badge</Text>
            <Text style={styles.badgeDescription}>✓ Enhanced verification with public visibility</Text>
            <Text style={styles.badgeStatus}>Will be assigned</Text>
          </View>
          <View style={styles.badgeCard}>
            <Text style={styles.badgeType}>Business Account Badge</Text>
            <Text style={styles.badgeDescription}>✓ Business account verification</Text>
            <Text style={styles.badgeStatus}>Will be assigned</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Payment Confirmation:</Text>
        <View style={styles.paymentConfirmation}>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Private Profile Verification:</Text>
            <Text style={styles.paymentAmount}>14,49€</Text>
          </View>
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Public Page Verification:</Text>
            <Text style={styles.paymentAmount}>24,49€</Text>
          </View>
          <View style={styles.paymentSeparator} />
          <View style={styles.paymentRow}>
            <Text style={styles.paymentLabel}>Bundle Discount:</Text>
            <Text style={styles.paymentDiscount}>-1,51€</Text>
          </View>
          <View style={styles.paymentSeparator} />
          <View style={styles.paymentRow}>
            <Text style={styles.paymentTotalLabel}>Total Paid:</Text>
            <Text style={styles.paymentTotal}>40,49€</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>User Notification Preview:</Text>
        <View style={styles.notificationPreview}>
          <Text style={styles.notificationTitle}>Verification Approved! 🎉</Text>
          <Text style={styles.notificationMessage}>
            Congratulations! Your verification request has been approved. You now have:
            {'\n\n'}• Private Profile Verification Badge
            {'\n'}• Public Page Verification Badge
            {'\n'}• Business Account Status
            {'\n\n'}Your account now has enhanced features and credibility indicators.
          </Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Confirmation → User notification sent</Text>
          <Text style={styles.navItem}>• Badge assignment → Badge management system</Text>
          <Text style={styles.navItem}>• Payment processing → Payment confirmation</Text>
          <Text style={styles.navItem}>• Back → Verification Approval Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>System Actions:</Text>
        <View style={styles.systemActions}>
          <Text style={styles.systemAction}>• Assign verification badges to user profile</Text>
          <Text style={styles.systemAction}>• Update account type to Business (if applicable)</Text>
          <Text style={styles.systemAction}>• Send in-app and email notification</Text>
          <Text style={styles.systemAction}>• Log verification completion for analytics</Text>
          <Text style={styles.systemAction}>• Update user permissions and features</Text>
        </View>

        <Text style={styles.sectionTitle}>Final Actions:</Text>
        <View style={styles.finalActions}>
          <TouchableOpacity style={styles.confirmButton}>
            <Text style={styles.confirmButtonText}>Confirm & Send Notification</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit Details</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#27ae60',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  summaryCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ecf0f1',
  },
  summaryLabel: {
    fontSize: 14,
    color: '#7f8c8d',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  badgeAssignment: {
    marginBottom: 16,
  },
  badgeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#3498db',
  },
  badgeType: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#3498db',
    marginBottom: 4,
  },
  badgeDescription: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  badgeStatus: {
    fontSize: 12,
    color: '#27ae60',
    fontWeight: 'bold',
  },
  paymentConfirmation: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#555',
  },
  paymentAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  paymentDiscount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#e74c3c',
  },
  paymentSeparator: {
    height: 1,
    backgroundColor: '#ecf0f1',
    marginVertical: 8,
  },
  paymentTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  paymentTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
  },
  notificationPreview: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#f39c12',
  },
  notificationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#f39c12',
    marginBottom: 12,
  },
  notificationMessage: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  systemActions: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  systemAction: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  finalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmButton: {
    backgroundColor: '#27ae60',
    padding: 16,
    borderRadius: 8,
    width: '45%',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  editButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    width: '25%',
    alignItems: 'center',
  },
  editButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '25%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
