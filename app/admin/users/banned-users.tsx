import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function BannedUsers() {
  const bannedUsers = [
    {
      id: 'user002',
      name: '<PERSON>',
      reason: 'Spamming',
      type: 'Temporary',
      duration: '30 days',
      appeal: 'None',
      date: '2023-12-15',
    },
    {
      id: 'user007',
      name: '<PERSON>',
      reason: 'Hate Speech',
      type: 'Permanent',
      duration: 'N/A',
      appeal: 'Pending',
      date: '2023-11-20',
    },
    {
      id: 'user008',
      name: '<PERSON>',
      reason: 'Multiple Violations',
      type: 'Permanent',
      duration: 'N/A',
      appeal: 'Denied',
      date: '2023-10-01',
    },
  ];

  const banCategories = [
    { name: 'Temporary Bans', count: 45, description: 'Time-limited suspensions' },
    { name: 'Permanent Bans', count: 12, description: 'Indefinite account suspension' },
    { name: 'Appeal Pending', count: 8, description: 'Users with active appeals' },
    { name: 'Unban Requests', count: 3, description: 'Requests for ban removal' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Banned Users</Text>
        <Text style={styles.subtitle}>Manage Banned Users and Appeals</Text>
        <Link href="/admin/users/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is used to manage all banned users on the platform. It provides tools for
          reviewing ban reasons, managing appeals, and processing unban requests.
        </Text>

        <Text style={styles.sectionTitle}>Ban Categories:</Text>
        <View style={styles.categoriesGrid}>
          {banCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryCount}>{cat.count}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Banned User Directory:</Text>
        {bannedUsers.map((user) => (
          <Link key={user.id} href={`/admin/users/${user.id}`} asChild>
            <TouchableOpacity style={styles.userCard}>
              <View style={styles.userHeader}>
                <Text style={styles.userName}>{user.name}</Text>
                <Text style={[styles.banType, { color: user.type === 'Permanent' ? '#c0392b' : '#e67e22' }]}>
                  {user.type}
                </Text>
              </View>
              <Text style={styles.userDetails}>Reason: {user.reason}</Text>
              <Text style={styles.userDetails}>Duration: {user.duration}</Text>
              <Text style={styles.userDetails}>Appeal Status: {user.appeal}</Text>
              <Text style={styles.userDetails}>Ban Date: {user.date}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Banned user directory with search</Text>
          <Text style={styles.feature}>• Ban reason and duration tracking</Text>
          <Text style={styles.feature}>• Appeal management system</Text>
          <Text style={styles.feature}>• Unban request processing</Text>
          <Text style={styles.feature}>• Ban history and patterns</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • User selection → Banned User Detail Screen (User Profile)
          {
            '\n'}
          • Appeal Review → Appeal processing interface with decision workflow
          {
            '\n'}
          • Unban Process → Unban confirmation workflow with logging
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, alignItems: 'center' },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryCount: { fontSize: 24, fontWeight: 'bold', color: '#e74c3c', marginVertical: 8 },
  categoryDescription: { fontSize: 12, color: '#7f8c8d', textAlign: 'center' },
  userCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  userHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  userName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  banType: { fontSize: 14, fontWeight: 'bold' },
  userDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});