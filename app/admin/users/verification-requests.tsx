import { <PERSON> } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function VerificationRequests() {
  const requests = [
    {
      id: 'vr001',
      userId: 'user004',
      userName: '<PERSON>',
      type: 'Public',
      status: 'Pending',
      payment: 'Paid',
      date: '2024-01-18',
      aiScore: 0.85,
    },
    {
      id: 'vr002',
      userId: 'user005',
      userName: '<PERSON>',
      type: 'Private',
      status: 'Pending',
      payment: 'Paid',
      date: '2024-01-17',
      aiScore: 0.92,
    },
    {
      id: 'vr003',
      userId: 'user006',
      userName: '<PERSON>',
      type: 'Both',
      status: 'In Review',
      payment: 'Paid',
      date: '2024-01-16',
      aiScore: 0.78,
    },
    {
      id: 'vr004',
      userId: 'user007',
      userName: '<PERSON>',
      type: 'Business',
      status: 'Pending',
      payment: 'Paid',
      date: '2024-01-15',
      aiScore: 0.89,
    },
  ];

  const verificationTypes = [
    { name: 'Private Profile Verification', description: '14,49€ - Basic identity confirmation', price: '14,49€' },
    { name: 'Public Page Verification', description: '24,49€ - Enhanced verification with public badge', price: '24,49€' },
    { name: 'Both Verifications', description: '40,49€ - Private + Public verification bundle', price: '40,49€' },
    { name: 'Business Verification', description: 'Separate pricing - Business account verification', price: 'Variable' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verification Requests</Text>
        <Text style={styles.subtitle}>Manage User Verification Submissions</Text>
        <Link href="/admin/users/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is used to manage all user verification requests. It provides tools for
          document review, payment status tracking, and AI-assisted validation to ensure a
          secure and efficient verification process.
        </Text>

        <Text style={styles.sectionTitle}>Pending Requests:</Text>
        {requests.map((req) => (
          <Link key={req.id} href={`/admin/users/verification-requests/${req.id}`} asChild>
            <TouchableOpacity style={styles.requestCard}>
              <View style={styles.requestHeader}>
                <Text style={styles.userName}>{req.userName}</Text>
                <Text style={styles.requestType}>{req.type}</Text>
              </View>
              <Text style={styles.requestDetails}>
                User ID: {req.userId} | Submitted: {req.date}
              </Text>
              <View style={styles.requestStatus}>
                <Text style={styles.statusLabel}>Status:</Text>
                <Text style={styles.statusValue}>{req.status}</Text>
                <Text style={styles.statusLabel}>Payment:</Text>
                <Text style={styles.statusValue}>{req.payment}</Text>
                <Text style={styles.statusLabel}>AI Score:</Text>
                <Text style={styles.statusValue}>{req.aiScore}</Text>
              </View>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Verification Types:</Text>
        <View style={styles.typesContainer}>
          {verificationTypes.map((type, index) => (
            <View key={index} style={styles.typeCard}>
              <Text style={styles.typeName}>{type.name}</Text>
              <Text style={styles.typeDescription}>{type.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Document verification interface</Text>
          <Text style={styles.feature}>• Payment status tracking</Text>
          <Text style={styles.feature}>• AI-assisted document validation</Text>
          <Text style={styles.feature}>• Batch processing capabilities</Text>
          <Text style={styles.feature}>• Verification history tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Request selection → Verification Detail Screen
          {
            '\n• Approve/Reject → Confirmation dialogs and user notification\n• Document Review → Secure document viewer interface'
          }
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#3498db',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  requestCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  requestType: {
    fontSize: 14,
    color: '#3498db',
    fontWeight: 'bold',
  },
  requestDetails: {
    fontSize: 12,
    color: '#7f8c8d',
    marginBottom: 8,
  },
  requestStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 12,
    color: '#7f8c8d',
    marginRight: 4,
  },
  statusValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginRight: 16,
  },
  typesContainer: {
    marginBottom: 16,
  },
  typeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  typeName: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  typeDescription: {
    fontSize: 12,
    color: '#555',
    marginTop: 4,
  },
  featuresList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});