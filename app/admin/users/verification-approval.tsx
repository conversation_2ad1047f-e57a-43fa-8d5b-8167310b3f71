import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function VerificationApproval() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verification Approval</Text>
        <Text style={styles.subtitle}>Approve user verification requests</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Process and approve user verification requests. This screen handles the approval 
          workflow for Private Profile Verification (14,49€), Public Page Verification (24,49€), 
          Both Verifications (40,49€), and Business Verification with separate pricing.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Verification request details</Text>
          <Text style={styles.component}>• User information summary</Text>
          <Text style={styles.component}>• Payment verification status</Text>
          <Text style={styles.component}>• Document review interface</Text>
          <Text style={styles.component}>• Approval confirmation options</Text>
        </View>

        <Text style={styles.sectionTitle}>Verification Types & Pricing:</Text>
        <View style={styles.verificationTypes}>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Private Profile Verification</Text>
            <Text style={styles.typePrice}>14,49€</Text>
            <Text style={styles.typeDescription}>Basic identity confirmation for private profiles</Text>
          </View>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Public Page Verification</Text>
            <Text style={styles.typePrice}>24,49€</Text>
            <Text style={styles.typeDescription}>Enhanced verification with public badge</Text>
          </View>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Both Verifications</Text>
            <Text style={styles.typePrice}>40,49€</Text>
            <Text style={styles.typeDescription}>Private + Public verification bundle</Text>
          </View>
          <View style={styles.typeCard}>
            <Text style={styles.typeName}>Business Verification</Text>
            <Text style={styles.typePrice}>Variable Pricing</Text>
            <Text style={styles.typeDescription}>Business account verification with separate pricing structure</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Approval Process:</Text>
        <View style={styles.processSteps}>
          <Text style={styles.step}>1. Review user documentation and identity verification</Text>
          <Text style={styles.step}>2. Verify payment confirmation (14,49€ / 24,49€ / 40,49€)</Text>
          <Text style={styles.step}>3. Check AI confidence score and fraud indicators</Text>
          <Text style={styles.step}>4. Approve verification and assign appropriate badge</Text>
          <Text style={styles.step}>5. Send confirmation notification to user</Text>
        </View>

        <Text style={styles.sectionTitle}>Approval Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#27ae60' }]}>
            <Text style={styles.actionTitle}>Approve Private</Text>
            <Text style={styles.actionDescription}>14,49€ - Private verification</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#3498db' }]}>
            <Text style={styles.actionTitle}>Approve Public</Text>
            <Text style={styles.actionDescription}>24,49€ - Public verification</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#9b59b6' }]}>
            <Text style={styles.actionTitle}>Approve Both</Text>
            <Text style={styles.actionDescription}>40,49€ - Combined verification</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#e67e22' }]}>
            <Text style={styles.actionTitle}>Approve Business</Text>
            <Text style={styles.actionDescription}>Variable - Business verification</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Approval confirmation → Verification Confirmation Screen</Text>
          <Text style={styles.navItem}>• Document review → Document Review Screen</Text>
          <Text style={styles.navItem}>• Payment verification → Payment Verification Screen</Text>
          <Text style={styles.navItem}>• Back → Verification Requests Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Approval Criteria:</Text>
        <View style={styles.criteriaList}>
          <Text style={styles.criteria}>• Valid government-issued ID or business documentation</Text>
          <Text style={styles.criteria}>• Payment confirmation for appropriate verification type</Text>
          <Text style={styles.criteria}>• AI confidence score above threshold (typically 0.8+)</Text>
          <Text style={styles.criteria}>• No fraud indicators or suspicious activity</Text>
          <Text style={styles.criteria}>• Account meets minimum age and activity requirements</Text>
        </View>

        <Text style={styles.sectionTitle}>Final Actions:</Text>
        <View style={styles.finalActions}>
          <TouchableOpacity style={styles.confirmButton}>
            <Text style={styles.confirmButtonText}>Confirm Approval</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.reviewButton}>
            <Text style={styles.reviewButtonText}>Request Additional Review</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#27ae60',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  verificationTypes: {
    marginBottom: 16,
  },
  typeCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#27ae60',
  },
  typeName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
    marginBottom: 4,
  },
  typePrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  typeDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  processSteps: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  step: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#fff',
    textAlign: 'center',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  criteriaList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  criteria: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  finalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  confirmButton: {
    backgroundColor: '#27ae60',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  confirmButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  reviewButton: {
    backgroundColor: '#f39c12',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  reviewButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
