import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function VerificationRejection() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verification Rejection</Text>
        <Text style={styles.subtitle}>Reject user verification requests with detailed reasons</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Process and reject user verification requests with detailed reasoning. This screen 
          handles the rejection workflow for all verification types and provides clear 
          feedback to users about why their verification was denied.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Verification request details</Text>
          <Text style={styles.component}>• Rejection reason selection</Text>
          <Text style={styles.component}>• Custom rejection message</Text>
          <Text style={styles.component}>• Refund processing options</Text>
          <Text style={styles.component}>• Rejection confirmation</Text>
        </View>

        <Text style={styles.sectionTitle}>Common Rejection Reasons:</Text>
        <View style={styles.rejectionReasons}>
          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Invalid Documentation</Text>
            <Text style={styles.reasonDescription}>
              Submitted documents are unclear, expired, or do not meet verification standards
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Identity Mismatch</Text>
            <Text style={styles.reasonDescription}>
              Information provided does not match official documentation
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Fraudulent Activity</Text>
            <Text style={styles.reasonDescription}>
              Suspicious activity detected or fraudulent documentation submitted
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Incomplete Application</Text>
            <Text style={styles.reasonDescription}>
              Missing required information or documentation for verification
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Payment Issues</Text>
            <Text style={styles.reasonDescription}>
              Payment verification failed or incorrect amount paid
            </Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.reasonCard}>
            <Text style={styles.reasonTitle}>Business Documentation</Text>
            <Text style={styles.reasonDescription}>
              Business verification documents are invalid or insufficient
            </Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Refund Processing:</Text>
        <View style={styles.refundOptions}>
          <View style={styles.refundCard}>
            <Text style={styles.refundTitle}>Automatic Refund</Text>
            <Text style={styles.refundDescription}>
              Process automatic refund for rejected verification (14,49€ / 24,49€ / 40,49€)
            </Text>
            <TouchableOpacity style={styles.refundButton}>
              <Text style={styles.refundButtonText}>Process Refund</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.refundCard}>
            <Text style={styles.refundTitle}>Manual Review</Text>
            <Text style={styles.refundDescription}>
              Flag for manual refund review due to special circumstances
            </Text>
            <TouchableOpacity style={styles.reviewRefundButton}>
              <Text style={styles.reviewRefundButtonText}>Manual Review</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Rejection Process:</Text>
        <View style={styles.processSteps}>
          <Text style={styles.step}>1. Select appropriate rejection reason from predefined list</Text>
          <Text style={styles.step}>2. Add custom message with specific feedback for user</Text>
          <Text style={styles.step}>3. Process refund according to verification type pricing</Text>
          <Text style={styles.step}>4. Send rejection notification with improvement suggestions</Text>
          <Text style={styles.step}>5. Log rejection reason for analytics and pattern detection</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Rejection confirmation → Rejection Confirmation Screen</Text>
          <Text style={styles.navItem}>• Refund processing → Refund Processing Screen</Text>
          <Text style={styles.navItem}>• User notification → User Notification Screen</Text>
          <Text style={styles.navItem}>• Back → Verification Requests Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Custom Message:</Text>
        <View style={styles.messageSection}>
          <Text style={styles.messageLabel}>Additional feedback for user:</Text>
          <View style={styles.messageInput}>
            <Text style={styles.messagePlaceholder}>
              Provide specific guidance on how to improve their verification submission...
            </Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Final Actions:</Text>
        <View style={styles.finalActions}>
          <TouchableOpacity style={styles.rejectButton}>
            <Text style={styles.rejectButtonText}>Confirm Rejection</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.requestInfoButton}>
            <Text style={styles.requestInfoButtonText}>Request More Info</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.cancelButton}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  rejectionReasons: {
    marginBottom: 16,
  },
  reasonCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
  },
  reasonTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 8,
  },
  reasonDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  refundOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  refundCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    borderLeftWidth: 4,
    borderLeftColor: '#f39c12',
  },
  refundTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#f39c12',
    marginBottom: 8,
  },
  refundDescription: {
    fontSize: 12,
    color: '#555',
    lineHeight: 16,
    marginBottom: 12,
  },
  refundButton: {
    backgroundColor: '#f39c12',
    padding: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  refundButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  reviewRefundButton: {
    backgroundColor: '#95a5a6',
    padding: 8,
    borderRadius: 4,
    alignItems: 'center',
  },
  reviewRefundButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  processSteps: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  step: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  messageSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  messageLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 8,
  },
  messageInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 4,
    padding: 12,
    minHeight: 80,
  },
  messagePlaceholder: {
    fontSize: 14,
    color: '#95a5a6',
    fontStyle: 'italic',
  },
  finalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rejectButton: {
    backgroundColor: '#e74c3c',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  rejectButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  requestInfoButton: {
    backgroundColor: '#3498db',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  requestInfoButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cancelButton: {
    backgroundColor: '#95a5a6',
    padding: 16,
    borderRadius: 8,
    width: '30%',
    alignItems: 'center',
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});
