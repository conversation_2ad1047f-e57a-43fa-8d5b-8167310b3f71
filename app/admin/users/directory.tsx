import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function UserDirectory() {
  const sampleUsers = [
    {
      id: 'user001',
      pid: 'PID12345',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1234567890',
      status: 'Active',
      goldMember: true,
      verification: 'Public',
      country: 'USA',
      joinDate: '2023-01-15'
    },
    {
      id: 'user002',
      pid: 'PID67890',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+0987654321',
      status: 'Banned',
      goldMember: false,
      verification: 'None',
      country: 'Canada',
      joinDate: '2023-02-20'
    },
    {
      id: 'user003',
      pid: 'PID11111',
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '+1122334455',
      status: 'Active',
      goldMember: false,
      verification: 'Private',
      country: 'UK',
      joinDate: '2023-03-10'
    }
  ];

  const filterOptions = [
    { label: 'Status', options: ['All', 'Active', 'Banned', 'Deleted', 'Pending Recovery'] },
    { label: 'Gold Membership', options: ['All', 'Yes', 'No'] },
    { label: 'Verification', options: ['All', 'Private', 'Public', 'Both', 'None'] },
    { label: 'Country', options: ['All', 'USA', 'Canada', 'UK', 'Germany', 'France'] }
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Directory</Text>
        <Text style={styles.subtitle}>Search and Manage Platform Users</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          The User Directory provides comprehensive search and filtering capabilities for managing 
          all platform users. Administrators can search by ID, phone, email, or PID, and apply 
          various filters to find specific user groups.
        </Text>

        <Text style={styles.sectionTitle}>Search & Filter Options:</Text>
        <View style={styles.searchSection}>
          <Text style={styles.searchLabel}>Search by: ID, Phone, Email, PID</Text>
          <View style={styles.searchBox}>
            <Text style={styles.searchPlaceholder}>Enter search term...</Text>
          </View>
        </View>

        <Text style={styles.filterTitle}>Filter Options:</Text>
        <View style={styles.filtersGrid}>
          {filterOptions.map((filter, index) => (
            <View key={index} style={styles.filterCard}>
              <Text style={styles.filterLabel}>{filter.label}</Text>
              <Text style={styles.filterValue}>All</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>User Management Actions:</Text>
        <View style={styles.actionsGrid}>
          <Link href="/admin/users/verification-requests" asChild>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#3498db' }]}>
              <Text style={styles.actionTitle}>Verification Requests</Text>
              <Text style={styles.actionCount}>23 pending</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/users/banned-users" asChild>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionTitle}>Banned Users</Text>
              <Text style={styles.actionCount}>156 total</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/users/gold-membership" asChild>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#f1c40f' }]}>
              <Text style={styles.actionTitle}>Gold Membership</Text>
              <Text style={styles.actionCount}>5,432 members</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/users/account-recovery" asChild>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#9b59b6' }]}>
              <Text style={styles.actionTitle}>Account Recovery</Text>
              <Text style={styles.actionCount}>12 requests</Text>
            </TouchableOpacity>
          </Link>
          <Link href="/admin/users/deleted-accounts" asChild>
            <TouchableOpacity style={[styles.actionCard, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.actionTitle}>Deleted Accounts</Text>
              <Text style={styles.actionCount}>89 pending</Text>
            </TouchableOpacity>
          </Link>
        </View>

        <Text style={styles.sectionTitle}>Sample User Data:</Text>
        {sampleUsers.map((user) => (
          <Link key={user.id} href={`/admin/users/${user.id}`} asChild>
            <TouchableOpacity style={styles.userCard}>
              <View style={styles.userHeader}>
                <Text style={styles.userName}>{user.name}</Text>
                <View style={[
                  styles.statusBadge, 
                  { backgroundColor: user.status === 'Active' ? '#27ae60' : '#e74c3c' }
                ]}>
                  <Text style={styles.statusText}>{user.status}</Text>
                </View>
              </View>
              <Text style={styles.userDetail}>ID: {user.id} | PID: {user.pid}</Text>
              <Text style={styles.userDetail}>Email: {user.email}</Text>
              <Text style={styles.userDetail}>Phone: {user.phone}</Text>
              <View style={styles.userMeta}>
                <Text style={styles.metaItem}>
                  Gold: {user.goldMember ? '✓' : '✗'}
                </Text>
                <Text style={styles.metaItem}>
                  Verification: {user.verification}
                </Text>
                <Text style={styles.metaItem}>
                  Country: {user.country}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Bulk Actions:</Text>
        <View style={styles.bulkActions}>
          <TouchableOpacity style={styles.bulkButton}>
            <Text style={styles.bulkButtonText}>Export Selected</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.bulkButton}>
            <Text style={styles.bulkButtonText}>Send Notification</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.bulkButton}>
            <Text style={styles.bulkButtonText}>Apply Action</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • User selection → User Detail Screen (with all tabs)
          {'\n'}• Verification requests → Verification Requests Screen
          {'\n'}• Banned users → Banned Users Screen
          {'\n'}• Gold membership → Gold Membership Screen
          {'\n'}• Account recovery → Account Recovery Screen
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#3498db',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  searchSection: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  searchLabel: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  searchBox: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 6,
    padding: 12,
  },
  searchPlaceholder: {
    color: '#999',
    fontSize: 14,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  filtersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  filterCard: {
    backgroundColor: '#fff',
    padding: 12,
    borderRadius: 6,
    width: '48%',
    marginBottom: 8,
  },
  filterLabel: {
    fontSize: 12,
    color: '#777',
    marginBottom: 4,
  },
  filterValue: {
    fontSize: 14,
    color: '#2c3e50',
    fontWeight: 'bold',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionCard: {
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    alignItems: 'center',
  },
  actionTitle: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  actionCount: {
    color: '#fff',
    fontSize: 12,
    marginTop: 4,
  },
  userCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  userDetail: {
    fontSize: 14,
    color: '#555',
    marginBottom: 4,
  },
  userMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  metaItem: {
    fontSize: 12,
    color: '#777',
  },
  bulkActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  bulkButton: {
    backgroundColor: '#95a5a6',
    padding: 12,
    borderRadius: 6,
    width: '30%',
    alignItems: 'center',
  },
  bulkButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  flowDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
});
