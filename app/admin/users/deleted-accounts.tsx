import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function DeletedAccounts() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Deleted Accounts</Text>
        <Text style={styles.subtitle}>Track accounts marked for deletion with 60-day recovery window</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Track accounts marked for deletion with 60-day recovery window. This screen 
          manages the deletion lifecycle and recovery process for user accounts.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• List of deleted accounts</Text>
          <Text style={styles.component}>• Deletion timestamp</Text>
          <Text style={styles.component}>• Recovery deadline (60 days from deletion)</Text>
          <Text style={styles.component}>• Recovery status (Pending, Restored, Permanently Deleted)</Text>
          <Text style={styles.component}>• Filter options (recovery deadline, region)</Text>
          <Text style={styles.component}>• "Restore Account" button (within 60 days)</Text>
          <Text style={styles.component}>• "Mark as Permanently Deleted" button (after 60 days)</Text>
        </View>

        <Text style={styles.sectionTitle}>Account Recovery Process:</Text>
        <View style={styles.processSteps}>
          <Text style={styles.step}>1. Account deletion request received</Text>
          <Text style={styles.step}>2. Account marked as deleted (data retained)</Text>
          <Text style={styles.step}>3. 60-day recovery window begins</Text>
          <Text style={styles.step}>4. User can recover using secret phrase, ID, and PIN</Text>
          <Text style={styles.step}>5. After 60 days: permanent deletion eligible</Text>
        </View>

        <Text style={styles.sectionTitle}>Recovery Status Types:</Text>
        <View style={styles.statusList}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#f39c12' }]} />
            <Text style={styles.statusText}>Pending: Within 60-day recovery window</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#27ae60' }]} />
            <Text style={styles.statusText}>Restored: Account successfully recovered</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#e74c3c' }]} />
            <Text style={styles.statusText}>Permanently Deleted: Data permanently removed</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Account selection → Deleted Account Detail Screen</Text>
          <Text style={styles.navItem}>• Restore → Account Restoration Screen</Text>
          <Text style={styles.navItem}>• Back → User Directory Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Key Requirements:</Text>
        <View style={styles.requirementsList}>
          <Text style={styles.requirement}>• Deleted accounts are retained for 60 days before permanent deletion</Text>
          <Text style={styles.requirement}>• Recovery is only possible within 60 days using secret phrase, ID, and PIN</Text>
          <Text style={styles.requirement}>• Comprehensive audit logging for all deletion and recovery actions</Text>
          <Text style={styles.requirement}>• Regional filtering for compliance with local data protection laws</Text>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>View Pending</Text>
            <Text style={styles.actionDescription}>Accounts within recovery window</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>View Expired</Text>
            <Text style={styles.actionDescription}>Accounts past 60-day window</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Recovery Requests</Text>
            <Text style={styles.actionDescription}>Active recovery attempts</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Audit Log</Text>
            <Text style={styles.actionDescription}>Deletion and recovery history</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#e74c3c',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  processSteps: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  step: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  statusList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  requirementsList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  requirement: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#e74c3c',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#e74c3c',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
});
