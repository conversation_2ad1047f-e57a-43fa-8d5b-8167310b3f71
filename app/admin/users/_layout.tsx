import { Stack } from 'expo-router';

export default function UsersLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'User Directory',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="directory" 
        options={{ 
          title: 'User Directory',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="[id]" 
        options={{ 
          title: 'User Detail',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="verification-requests" 
        options={{ 
          title: 'Verification Requests',
          headerShown: false 
        }} 
      />
      <Stack.Screen
        name="verification-detail/[id]"
        options={{
          title: 'Verification Detail',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="verification-approval"
        options={{
          title: 'Verification Approval',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="verification-rejection"
        options={{
          title: 'Verification Rejection',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="verification-confirmation"
        options={{
          title: 'Verification Confirmation',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="rejection-confirmation"
        options={{
          title: 'Rejection Confirmation',
          headerShown: false
        }}
      />
      <Stack.Screen 
        name="banned-users" 
        options={{ 
          title: 'Banned Users',
          headerShown: false 
        }} 
      />
      <Stack.Screen 
        name="gold-membership" 
        options={{ 
          title: 'Gold Membership',
          headerShown: false 
        }} 
      />
      <Stack.Screen
        name="account-recovery"
        options={{
          title: 'Account Recovery',
          headerShown: false
        }}
      />
      <Stack.Screen
        name="deleted-accounts"
        options={{
          title: 'Deleted Accounts',
          headerShown: false
        }}
      />
    </Stack>
  );
}
