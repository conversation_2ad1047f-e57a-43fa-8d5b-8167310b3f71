import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function GoldMembership() {
  const members = [
    {
      id: 'user001',
      name: '<PERSON>',
      status: 'Active',
      purchaseDate: '2023-01-15',
      revenue: '80,00€',
      membershipType: 'Lifetime',
    },
    {
      id: 'user009',
      name: '<PERSON>',
      status: 'Active',
      purchaseDate: '2023-01-20',
      revenue: '80,00€',
      membershipType: 'Lifetime',
    },
    {
      id: 'user010',
      name: '<PERSON>',
      status: 'Active',
      purchaseDate: '2022-12-10',
      revenue: '80,00€',
      membershipType: 'Lifetime',
    },
  ];

  const membershipFeatures = [
    { name: 'Enhanced Verification', description: 'Priority verification process' },
    { name: 'Premium Features', description: 'Advanced platform capabilities' },
    { name: 'Priority Support', description: 'Dedicated customer service' },
    { name: 'Revenue Tracking', description: 'Subscription analytics' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Gold Membership</Text>
        <Text style={styles.subtitle}>Manage Gold Memberships and Payments</Text>
        <Link href="/admin/users/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is used to manage Gold membership applications, payments, and benefits.
          It provides tools for reviewing applications, verifying payments, and tracking renewals.
        </Text>

        <Text style={styles.sectionTitle}>Active Gold Members:</Text>
        {members.map((member) => (
          <Link key={member.id} href={`/admin/users/${member.id}`} asChild>
            <TouchableOpacity style={styles.memberCard}>
              <View style={styles.memberHeader}>
                <Text style={styles.memberName}>{member.name}</Text>
                <Text style={[styles.status, { color: member.status === 'Active' ? '#27ae60' : '#e74c3c' }]}>
                  {member.status}
                </Text>
              </View>
              <Text style={styles.memberDetails}>Renewal Date: {member.renewalDate}</Text>
              <Text style={styles.memberDetails}>Lifetime Revenue: {member.revenue}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Membership Features:</Text>
        <View style={styles.featuresContainer}>
          {membershipFeatures.map((feature, index) => (
            <View key={index} style={styles.featureCard}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Gold Membership Benefits (80€ Lifetime):</Text>
        <View style={styles.benefitsList}>
          <Text style={styles.benefit}>• Unlimited groups/channels</Text>
          <Text style={styles.benefit}>• Unlimited calls</Text>
          <Text style={styles.benefit}>• Remote profile deletion</Text>
          <Text style={styles.benefit}>• Unsubscribe from default accounts</Text>
        </View>

        <Text style={styles.sectionTitle}>Management Features:</Text>
        <View style={styles.managementFeaturesList}>
          <Text style={styles.feature}>• 80€ lifetime membership tracking</Text>
          <Text style={styles.feature}>• Benefits verification system</Text>
          <Text style={styles.feature}>• Subscription status monitoring</Text>
          <Text style={styles.feature}>• Payment verification interface</Text>
          <Text style={styles.feature}>• Membership activation workflow</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Application Review → Membership verification process with payment check
          {
            '\n'}
          • Payment Issues → Payment management screens for resolution
          {
            '\n'}
          • Member Support → Support ticket system for priority assistance
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f1c40f', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  memberCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  memberHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  memberName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  status: { fontSize: 14, fontWeight: 'bold' },
  memberDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  featuresContainer: { marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  featureName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  featureDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  benefitsList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  benefit: { fontSize: 14, color: '#555', marginBottom: 8, fontWeight: '500' },
  managementFeaturesList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});