import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function AccountRecovery() {
  const recoveryRequests = [
    {
      id: 'rec001',
      userId: 'user011',
      userName: '<PERSON> White',
      status: 'Pending Phrase Verification',
      date: '2024-01-18',
    },
    {
      id: 'rec002',
      userId: 'user012',
      userName: 'Daniel Green',
      status: 'Pending Admin Review',
      date: '2024-01-17',
    },
  ];

  const recoveryProcess = [
    { step: 1, name: 'Request Submission', description: 'User submits recovery request' },
    { step: 2, name: 'Identity Verification', description: 'Multiple verification methods' },
    { step: 3, name: 'Secret Phrase', description: 'User-provided recovery phrase' },
    { step: 4, name: 'Admin Review', description: 'Manual verification process' },
    { step: 5, name: 'Account Restoration', description: 'Secure account recovery' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Account Recovery</Text>
        <Text style={styles.subtitle}>Manage Account Recovery Requests</Text>
        <Link href="/admin/users/directory" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Directory</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is used to handle account recovery requests from users who have lost
          access to their accounts. It involves a secure process, including secret phrase
          verification, to ensure legitimate recovery.
        </Text>

        <Text style={styles.sectionTitle}>Pending Recovery Requests:</Text>
        {recoveryRequests.map((req) => (
          <TouchableOpacity key={req.id} style={styles.requestCard}>
            <Text style={styles.userName}>{req.userName} ({req.userId})</Text>
            <Text style={styles.requestDetails}>Submitted: {req.date}</Text>
            <Text style={styles.requestStatus}>Status: {req.status}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Recovery Process:</Text>
        <View style={styles.processContainer}>
          {recoveryProcess.map((item) => (
            <View key={item.step} style={styles.processStep}>
              <Text style={styles.stepNumber}>{item.step}</Text>
              <View style={styles.stepInfo}>
                <Text style={styles.stepName}>{item.name}</Text>
                <Text style={styles.stepDescription}>{item.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Secret phrase verification system</Text>
          <Text style={styles.feature}>• Identity confirmation process</Text>
          <Text style={styles.feature}>• Recovery request tracking</Text>
          <Text style={styles.feature}>• Security validation protocols</Text>
          <Text style={styles.feature}>• Recovery history logging</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Request Processing → Recovery verification interface with secret phrase input
          {
            '\n'}
          • Security Validation → Identity confirmation screens (e.g., email, phone)
          {
            '\n'}
          • Account Restoration → User account management panel to restore access
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  requestCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  userName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  requestDetails: { fontSize: 12, color: '#7f8c8d', marginVertical: 4 },
  requestStatus: { fontSize: 14, color: '#9b59b6', fontWeight: 'bold' },
  processContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  processStep: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  stepNumber: { fontSize: 18, fontWeight: 'bold', color: '#9b59b6', marginRight: 12 },
  stepInfo: { flex: 1 },
  stepName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  stepDescription: { fontSize: 12, color: '#555' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});