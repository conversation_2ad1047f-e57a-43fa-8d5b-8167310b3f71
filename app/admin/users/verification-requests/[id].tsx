import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';

export default function VerificationDetail() {
  const { id } = useLocalSearchParams();

  const requestData = {
    id: id as string,
    userId: 'user004',
    userName: '<PERSON> Johnson',
    type: 'Public',
    status: 'Pending',
    payment: 'Paid',
    date: '2024-01-18',
    aiScore: 0.85,
    documentType: 'Passport',
    documentUrl: 'https://via.placeholder.com/400x250.png?text=Passport+Image',
  };

  const verificationChecklist = [
    { item: 'Document Review', status: 'Pending' },
    { item: 'AI Analysis', status: 'Complete' },
    { item: 'Payment Verification', status: 'Complete' },
    { item: 'Manual Review', status: 'Pending' },
    { item: 'User Notification', status: 'Pending' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Verification Detail</Text>
        <Text style={styles.subtitle}>Request ID: {requestData.id}</Text>
        <Link href="/admin/users/verification-requests" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Requests</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a detailed view of a single verification request, allowing for
          in-depth document analysis, AI-assisted validation, and final decision-making.
        </Text>

        <Text style={styles.sectionTitle}>Request Information:</Text>
        <View style={styles.infoCard}>
          <Text style={styles.infoText}>User: {requestData.userName} ({requestData.userId})</Text>
          <Text style={styles.infoText}>Type: {requestData.type}</Text>
          <Text style={styles.infoText}>Date: {requestData.date}</Text>
          <Text style={styles.infoText}>Payment: {requestData.payment}</Text>
          <Text style={styles.infoText}>AI Score: {requestData.aiScore}</Text>
        </View>

        <Text style={styles.sectionTitle}>Document Review:</Text>
        <View style={styles.documentCard}>
          <Text style={styles.documentType}>Document: {requestData.documentType}</Text>
          <Image source={{ uri: requestData.documentUrl }} style={styles.documentImage} />
          <View style={styles.documentActions}>
            <TouchableOpacity style={styles.docActionButton}><Text style={styles.docActionText}>Zoom</Text></TouchableOpacity>
            <TouchableOpacity style={styles.docActionButton}><Text style={styles.docActionText}>Annotate</Text></TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Verification Process:</Text>
        <View style={styles.checklistContainer}>
          {verificationChecklist.map((item, index) => (
            <View key={index} style={styles.checklistItem}>
              <Text style={styles.checklistItemText}>{item.item}</Text>
              <Text style={[styles.checklistStatus, { color: item.status === 'Complete' ? '#27ae60' : '#f39c12' }]}>
                {item.status}
              </Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Decision Workflow:</Text>
        <View style={styles.decisionCard}>
          <Text style={styles.decisionLabel}>Reason for Decision (optional):</Text>
          <View style={styles.reasonInput}><Text style={styles.reasonPlaceholder}>Enter reason...</Text></View>
          <View style={styles.decisionButtons}>
            <TouchableOpacity style={[styles.decisionButton, { backgroundColor: '#27ae60' }]}>
              <Text style={styles.decisionButtonText}>Approve</Text>
            </TouchableOpacity>
            <TouchableOpacity style={[styles.decisionButton, { backgroundColor: '#e74c3c' }]}>
              <Text style={styles.decisionButtonText}>Reject</Text>
            </TouchableOpacity>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
          <Link href={`/admin/users/${requestData.userId}`} asChild>
            <TouchableOpacity style={styles.navButton}><Text style={styles.navButtonText}>View User Profile</Text></TouchableOpacity>
          </Link>
          <Link href={`/admin/payments/transactions`} asChild>
            <TouchableOpacity style={styles.navButton}><Text style={styles.navButtonText}>View Payment Details</Text></TouchableOpacity>
          </Link>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#2980b9', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  infoCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  infoText: { fontSize: 14, color: '#555', marginBottom: 8 },
  documentCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, alignItems: 'center' },
  documentType: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50', marginBottom: 12 },
  documentImage: { width: '100%', height: 200, borderRadius: 8, marginBottom: 12, backgroundColor: '#ecf0f1' },
  documentActions: { flexDirection: 'row', justifyContent: 'space-around', width: '100%' },
  docActionButton: { backgroundColor: '#ecf0f1', padding: 10, borderRadius: 5 },
  docActionText: { fontSize: 12, color: '#555' },
  checklistContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8 },
  checklistItem: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  checklistItemText: { fontSize: 14, color: '#2c3e50' },
  checklistStatus: { fontSize: 14, fontWeight: 'bold' },
  decisionCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginTop: 16 },
  decisionLabel: { fontSize: 14, color: '#555', marginBottom: 8 },
  reasonInput: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 12, marginBottom: 12, backgroundColor: '#f8f9fa' },
  reasonPlaceholder: { color: '#999' },
  decisionButtons: { flexDirection: 'row', justifyContent: 'space-around' },
  decisionButton: { paddingVertical: 12, paddingHorizontal: 24, borderRadius: 6 },
  decisionButtonText: { color: '#fff', fontWeight: 'bold' },
  navigationSection: { marginTop: 24 },
  navButton: { backgroundColor: '#95a5a6', padding: 12, borderRadius: 6, alignItems: 'center', marginBottom: 8 },
  navButtonText: { color: '#fff', fontSize: 14 },
});