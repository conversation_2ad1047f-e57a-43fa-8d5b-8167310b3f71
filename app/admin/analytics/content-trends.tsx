import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ContentTrends() {
  const contentCategories = [
    { name: 'Images', count: '18.5K', percentage: '42%' },
    { name: 'Text Posts', count: '12.3K', percentage: '28%' },
    { name: 'Videos', count: '8.7K', percentage: '20%' },
    { name: 'Links', count: '4.4K', percentage: '10%' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Content Trends</Text>
        <Text style={styles.subtitle}>Content Category Analysis and Growth Metrics</Text>
        <Link href="/admin/analytics/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Analytics</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides analysis of content categories and growth metrics. It includes
          tools for tracking content type distribution, identifying popular content, and monitoring
          engagement rates.
        </Text>

        <Text style={styles.sectionTitle}>Content Categories:</Text>
        <View style={styles.categoriesGrid}>
          {contentCategories.map((category, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{category.name}</Text>
              <Text style={styles.categoryCount}>{category.count}</Text>
              <Text style={styles.categoryPercentage}>{category.percentage}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Content type distribution</Text>
          <Text style={styles.feature}>• Growth trend analysis</Text>
          <Text style={styles.feature}>• Popular content identification</Text>
          <Text style={styles.feature}>• Engagement rate tracking</Text>
          <Text style={styles.feature}>• Content performance metrics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Content Analysis → Detailed content performance and engagement data
          {
            '\n'}
          • Trend Reports → Content growth analytics and historical data
          {
            '\n'}
          • Engagement Metrics → Content interaction analysis and user feedback
          {
            '\n'}
          • Performance Tracking → Content success metrics and monetization data
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, alignItems: 'center' },
  categoryName: { fontSize: 14, color: '#7f8c8d', marginBottom: 8 },
  categoryCount: { fontSize: 24, fontWeight: 'bold', color: '#2c3e50' },
  categoryPercentage: { fontSize: 14, color: '#e74c3c', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});