import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function FinancialReports() {
  const financialMetrics = [
    { label: 'Monthly Revenue', value: '€125,000', trend: '+12.8%' },
    { label: 'Gold Memberships', value: '€89,000', trend: '+15.2%' },
    { label: 'Group/Channel Fees', value: '€28,000', trend: '+8.1%' },
    { label: 'Verification Fees', value: '€8,000', trend: '+5.5%' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Financial Reports</Text>
        <Text style={styles.subtitle}>Revenue Breakdown and Payment Analysis</Text>
        <Link href="/admin/analytics/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Analytics</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a detailed breakdown of revenue and payment analysis. It includes
          tools for tracking revenue trends, subscription performance, and financial forecasting.
        </Text>

        <Text style={styles.sectionTitle}>Financial Metrics:</Text>
        <View style={styles.metricsGrid}>
          {financialMetrics.map((metric, index) => (
            <View key={index} style={styles.metricCard}>
              <Text style={styles.metricLabel}>{metric.label}</Text>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricTrend}>{metric.trend}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Revenue analytics and trends</Text>
          <Text style={styles.feature}>• Payment method analysis</Text>
          <Text style={styles.feature}>• Subscription performance</Text>
          <Text style={styles.feature}>• Financial forecasting</Text>
          <Text style={styles.feature}>• Profit and loss reporting</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Revenue Analysis → Detailed revenue breakdown by source and region
          {
            '\n'}
          • Subscription Reports → Gold membership analytics and churn rates
          {
            '\n'}
          • Payment Analytics → Payment method performance and fees analysis
          {
            '\n'}
          • Forecasting → Financial projection tools and modeling
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f39c12', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, alignItems: 'center' },
  metricLabel: { fontSize: 14, color: '#7f8c8d', marginBottom: 8 },
  metricValue: { fontSize: 24, fontWeight: 'bold', color: '#2c3e50' },
  metricTrend: { fontSize: 14, color: '#27ae60', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});