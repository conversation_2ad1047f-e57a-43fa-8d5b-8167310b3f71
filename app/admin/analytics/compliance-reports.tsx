import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ComplianceReports() {
  const complianceMetrics = [
    { label: 'Overall Compliance Rate', value: '97.8%', trend: '+0.5%' },
    { label: 'Policy Violations (30d)', value: '380', trend: '-5.2%' },
    { label: 'Enforcement Actions', value: '156', trend: '+8.3%' },
    { label: 'Appeal Success Rate', value: '23%', trend: '+1.0%' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Compliance Reports</Text>
        <Text style={styles.subtitle}>Policy Adherence and Violation Statistics</Text>
        <Link href="/admin/analytics/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Analytics</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides statistics on policy adherence and violations. It includes tools
          for tracking compliance rates, enforcement effectiveness, and legal requirement adherence.
        </Text>

        <Text style={styles.sectionTitle}>Compliance Metrics:</Text>
        <View style={styles.metricsGrid}>
          {complianceMetrics.map((metric, index) => (
            <View key={index} style={styles.metricCard}>
              <Text style={styles.metricLabel}>{metric.label}</Text>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricTrend}>{metric.trend}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Policy violation tracking</Text>
          <Text style={styles.feature}>• Compliance rate monitoring</Text>
          <Text style={styles.feature}>• Enforcement effectiveness</Text>
          <Text style={styles.feature}>• Legal requirement adherence</Text>
          <Text style={styles.feature}>• Regulatory reporting tools</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Violation Analysis → Policy violation breakdown by category and severity
          {
            '\n'}
          • Enforcement Reports → Moderation action analytics and trends
          {
            '\n'}
          • Compliance Tracking → Regulatory adherence monitoring and reporting
          {
            '\n'}
          • Legal Reports → Legal requirement compliance and documentation
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, alignItems: 'center' },
  metricLabel: { fontSize: 14, color: '#7f8c8d', marginBottom: 8 },
  metricValue: { fontSize: 24, fontWeight: 'bold', color: '#2c3e50' },
  metricTrend: { fontSize: 14, color: '#27ae60', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});