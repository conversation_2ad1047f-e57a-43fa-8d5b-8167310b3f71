import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function UserEngagement() {
  const engagementMetrics = [
    { label: 'Daily Active Users', value: '234K', trend: '+5.2%' },
    { label: 'Session Duration', value: '15.2m', trend: '+2.1%' },
    { label: 'Feature Usage', value: '78%', trend: '-1.5%' },
    { label: 'Retention Rate', value: '45%', trend: '+3.0%' },
  ];

  const hashtagEngagementTrends = [
    { hashtag: '#Tech', engagement: '89.2K', trend: '+12.5%', category: 'Technology' },
    { hashtag: '#Music', engagement: '67.8K', trend: '+8.3%', category: 'Entertainment' },
    { hashtag: '#Politics', engagement: '45.6K', trend: '-2.1%', category: 'News' },
    { hashtag: '#Business', engagement: '34.2K', trend: '+15.7%', category: 'Business' },
    { hashtag: '#Sports', engagement: '28.9K', trend: '+6.4%', category: 'Sports' },
  ];

  const preferredContentCategories = [
    { category: 'Technology', percentage: '28%', users: '65.2K' },
    { category: 'Entertainment', percentage: '24%', users: '55.8K' },
    { category: 'News & Politics', percentage: '18%', users: '41.9K' },
    { category: 'Business', percentage: '15%', users: '34.9K' },
    { category: 'Sports', percentage: '15%', users: '34.9K' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>User Engagement</Text>
        <Text style={styles.subtitle}>User Activity Metrics and Retention Analysis</Text>
        <Link href="/admin/analytics/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Analytics</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides detailed metrics on user activity and retention. It includes tools
          for tracking user engagement, analyzing feature usage, and segmenting users.
        </Text>

        <Text style={styles.sectionTitle}>Engagement Metrics:</Text>
        <View style={styles.metricsGrid}>
          {engagementMetrics.map((metric, index) => (
            <View key={index} style={styles.metricCard}>
              <Text style={styles.metricLabel}>{metric.label}</Text>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricTrend}>{metric.trend}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Hashtag Engagement Trends:</Text>
        <View style={styles.hashtagTrends}>
          {hashtagEngagementTrends.map((item, index) => (
            <View key={index} style={styles.hashtagCard}>
              <View style={styles.hashtagHeader}>
                <Text style={styles.hashtagName}>{item.hashtag}</Text>
                <Text style={styles.hashtagCategory}>{item.category}</Text>
              </View>
              <View style={styles.hashtagMetrics}>
                <Text style={styles.hashtagEngagement}>{item.engagement} interactions</Text>
                <Text style={[styles.hashtagTrend, {
                  color: item.trend.startsWith('+') ? '#27ae60' : '#e74c3c'
                }]}>
                  {item.trend}
                </Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Preferred Content Categories:</Text>
        <View style={styles.contentCategories}>
          {preferredContentCategories.map((item, index) => (
            <View key={index} style={styles.categoryCard}>
              <View style={styles.categoryHeader}>
                <Text style={styles.categoryName}>{item.category}</Text>
                <Text style={styles.categoryPercentage}>{item.percentage}</Text>
              </View>
              <Text style={styles.categoryUsers}>{item.users} users</Text>
              <View style={styles.categoryBar}>
                <View style={[styles.categoryProgress, {
                  width: item.percentage
                }]} />
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• User activity tracking</Text>
          <Text style={styles.feature}>• Retention rate analysis</Text>
          <Text style={styles.feature}>• Feature usage statistics</Text>
          <Text style={styles.feature}>• Hashtag engagement trends analysis</Text>
          <Text style={styles.feature}>• Preferred content categories tracking</Text>
          <Text style={styles.feature}>• User segmentation tools</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Detailed Analysis → User engagement deep dive with cohort analysis
          {
            '\n'}
          • Segmentation → User group analysis and filtering
          {
            '\n'}
          • Retention Reports → User retention analytics and charts
          {
            '\n'}
          • Feature Analytics → Feature usage statistics and funnels
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, alignItems: 'center' },
  metricLabel: { fontSize: 14, color: '#7f8c8d', marginBottom: 8 },
  metricValue: { fontSize: 24, fontWeight: 'bold', color: '#2c3e50' },
  metricTrend: { fontSize: 14, color: '#27ae60', marginTop: 4 },
  hashtagTrends: { marginBottom: 16 },
  hashtagCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4, borderLeftColor: '#3498db' },
  hashtagHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 },
  hashtagName: { fontSize: 16, fontWeight: 'bold', color: '#3498db' },
  hashtagCategory: { fontSize: 12, color: '#7f8c8d', backgroundColor: '#ecf0f1', paddingHorizontal: 8, paddingVertical: 4, borderRadius: 12 },
  hashtagMetrics: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  hashtagEngagement: { fontSize: 14, color: '#555' },
  hashtagTrend: { fontSize: 14, fontWeight: 'bold' },
  contentCategories: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4, borderLeftColor: '#9b59b6' },
  categoryHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#9b59b6' },
  categoryPercentage: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  categoryUsers: { fontSize: 12, color: '#7f8c8d', marginBottom: 8 },
  categoryBar: { height: 6, backgroundColor: '#ecf0f1', borderRadius: 3, overflow: 'hidden' },
  categoryProgress: { height: '100%', backgroundColor: '#9b59b6' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});