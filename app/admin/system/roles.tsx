import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RoleManagement() {
  const adminRoles = [
    { name: 'Super Admin', description: 'Full system access and management' },
    { name: 'Senior Moderator', description: 'Advanced moderation and user management' },
    { name: 'Junior Moderator', description: 'Basic content moderation' },
    { name: 'Support Agent', description: 'User support and assistance' },
    { name: 'Finance Officer', description: 'Payment and financial management' },
    { name: 'Compliance Officer', description: 'Legal and policy management' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Role Management</Text>
        <Text style={styles.subtitle}>Admin Role Definitions and Permission Management</Text>
        <Link href="/admin/system/configuration" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Configuration</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing admin roles and their associated permissions. It includes
          tools for role creation, modification, and permission matrix management.
        </Text>

        <Text style={styles.sectionTitle}>Admin Roles:</Text>
        <View style={styles.rolesContainer}>
          {adminRoles.map((role, index) => (
            <TouchableOpacity key={index} style={styles.roleCard}>
              <Text style={styles.roleName}>{role.name}</Text>
              <Text style={styles.roleDescription}>{role.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Role creation and modification</Text>
          <Text style={styles.feature}>• Permission matrix management</Text>
          <Text style={styles.feature}>• Access level configuration</Text>
          <Text style={styles.feature}>• Role assignment tracking</Text>
          <Text style={styles.feature}>• Permission audit logging</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Role Editor → Role modification interface with permission checklist
          {
            '\n'}
          • Permission Matrix → Detailed permission management grid
          {
            '\n'}
          • Assignment Tracking → Role assignment history and audit log
          {
            '\n'}
          • Audit Log → Permission change tracking and history
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  rolesContainer: { marginBottom: 16 },
  roleCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  roleName: { fontSize: 16, fontWeight: 'bold', color: '#9b59b6' },
  roleDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});