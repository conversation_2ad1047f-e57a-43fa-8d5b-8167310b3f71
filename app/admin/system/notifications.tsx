import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function NotificationSettings() {
  const notificationTypes = [
    { name: 'User Notifications', description: 'Account updates, security alerts' },
    { name: 'Moderation Notifications', description: 'Policy violations, content actions' },
    { name: 'System Notifications', description: 'Maintenance, updates, alerts' },
    { name: 'Marketing Notifications', description: 'Promotions, feature announcements' },
  ];

  const deliveryMethods = [
    { name: 'Email', enabled: true },
    { name: 'SMS', enabled: true },
    { name: 'Push Notification', enabled: true },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Notification Settings</Text>
        <Text style={styles.subtitle}>Notification Templates and Delivery Methods</Text>
        <Link href="/admin/system/configuration" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Configuration</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages platform notification templates, delivery methods, and user
          preferences. It also provides analytics on notification delivery and engagement.
        </Text>

        <Text style={styles.sectionTitle}>Notification Types:</Text>
        <View style={styles.typesContainer}>
          {notificationTypes.map((type, index) => (
            <TouchableOpacity key={index} style={styles.typeCard}>
              <Text style={styles.typeName}>{type.name}</Text>
              <Text style={styles.typeDescription}>{type.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Delivery Methods:</Text>
        <View style={styles.deliveryContainer}>
          {deliveryMethods.map((method, index) => (
            <View key={index} style={styles.deliveryItem}>
              <Text style={styles.deliveryName}>{method.name}</Text>
              <Text style={styles.deliveryStatus}>{method.enabled ? 'Enabled' : 'Disabled'}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Notification template management</Text>
          <Text style={styles.feature}>• Delivery method configuration</Text>
          <Text style={styles.feature}>• User preference controls</Text>
          <Text style={styles.feature}>• Notification analytics</Text>
          <Text style={styles.feature}>• Template customization</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Template Editor → Notification template customization with variables
          
          • Delivery Settings → Email, SMS, push notification configuration
          
          • User Preferences → Notification preference management for users
          
          • Analytics → Notification delivery and engagement metrics
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f39c12', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  typesContainer: { marginBottom: 16 },
  typeCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  typeName: { fontSize: 16, fontWeight: 'bold', color: '#f39c12' },
  typeDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  deliveryContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  deliveryItem: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  deliveryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  deliveryStatus: { fontSize: 14, color: '#27ae60', fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});