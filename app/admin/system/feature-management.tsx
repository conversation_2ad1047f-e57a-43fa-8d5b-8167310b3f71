import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Link } from 'expo-router';
import { useState } from 'react';

export default function FeatureManagement() {
  const [userProfileV2, setUserProfileV2] = useState(true);
  const [aiModeration, setAiModeration] = useState(true);
  const [newPaymentGateway, setNewPaymentGateway] = useState(false);

  const featureCategories = [
    {
      title: 'User Features',
      features: [
        { id: 'user-profile-v2', name: 'User Profile V2', enabled: userProfileV2, onChange: setUserProfileV2, rollout: '100%' },
        { id: 'haptic-feedback', name: 'Haptic Feedback', enabled: true, onChange: () => {}, rollout: '100%' },
      ]
    },
    {
      title: 'Content Features',
      features: [
        { id: 'video-uploads-hd', name: 'HD Video Uploads', enabled: true, onChange: () => {}, rollout: '50%' },
        { id: 'image-filters', name: 'Image Filters', enabled: false, onChange: () => {}, rollout: '0%' },
      ]
    },
    {
      title: 'Moderation Features',
      features: [
        { id: 'ai-moderation', name: 'AI Content Moderation', enabled: aiModeration, onChange: setAiModeration, rollout: '100%' },
        { id: 'secret-group-monitoring', name: 'Secret Group Monitoring', enabled: true, onChange: () => {}, rollout: '100%' },
      ]
    },
    {
      title: 'Payment Features',
      features: [
        { id: 'new-payment-gateway', name: 'New Payment Gateway', enabled: newPaymentGateway, onChange: setNewPaymentGateway, rollout: '10%' },
      ]
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Feature Management</Text>
        <Text style={styles.subtitle}>Platform Feature Toggles and Rollout Management</Text>
        <Link href="/admin/system/configuration" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Configuration</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen allows for the management of platform features using feature flags.
          It supports gradual rollouts, A/B testing configuration, and usage analytics.
        </Text>

        {featureCategories.map((category, index) => (
          <View key={index}>
            <Text style={styles.sectionTitle}>{category.title}</Text>
            {category.features.map((feature) => (
              <View key={feature.id} style={styles.featureCard}>
                <View style={styles.featureInfo}>
                  <Text style={styles.featureName}>{feature.name}</Text>
                  <Text style={styles.rolloutStatus}>Rollout: {feature.rollout}</Text>
                </View>
                <Switch
                  value={feature.enabled}
                  onValueChange={feature.onChange}
                  trackColor={{ false: '#ddd', true: '#27ae60' }}
                  thumbColor={feature.enabled ? '#fff' : '#f4f3f4'}
                />
              </View>
            ))}
          </View>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Feature flag controls</Text>
          <Text style={styles.feature}>• Gradual rollout management</Text>
          <Text style={styles.feature}>• A/B testing configuration</Text>
          <Text style={styles.feature}>• Feature usage analytics</Text>
          <Text style={styles.feature}>• Rollback capabilities</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Feature Configuration → Individual feature settings with detailed controls
          
          • Rollout Management → Gradual deployment controls and user segmentation
          
          • Analytics → Feature usage statistics and A/B test results
          
          • Testing → A/B testing configuration and variant management
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  featureInfo: { flex: 1, marginRight: 16 },
  featureName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  rolloutStatus: { fontSize: 12, color: '#7f8c8d', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});