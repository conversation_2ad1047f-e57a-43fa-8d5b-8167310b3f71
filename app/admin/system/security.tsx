import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Link } from 'expo-router';
import { useState } from 'react';

export default function SecuritySettings() {
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);
  const [sessionTimeout, setSessionTimeout] = useState(true);

  const securityFeatures = [
    {
      id: 'two-factor-auth',
      name: 'Two-Factor Authentication',
      description: 'Mandatory 2FA for all admin accounts',
      enabled: twoFactorAuth,
      onChange: setTwoFactorAuth,
    },
    {
      id: 'session-timeout',
      name: 'Session Timeout',
      description: 'Automatic logout after 15 minutes of inactivity',
      enabled: sessionTimeout,
      onChange: setSessionTimeout,
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Security Settings</Text>
        <Text style={styles.subtitle}>Platform Security Protocols and Access Controls</Text>
        <Link href="/admin/system/configuration" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Configuration</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen manages platform-wide security protocols, including authentication,
          session management, IP restrictions, and threat detection.
        </Text>

        {securityFeatures.map((feature) => (
          <View key={feature.id} style={styles.featureCard}>
            <View style={styles.featureInfo}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </View>
            <Switch
              value={feature.enabled}
              onValueChange={feature.onChange}
              trackColor={{ false: '#ddd', true: '#27ae60' }}
              thumbColor={feature.enabled ? '#fff' : '#f4f3f4'}
            />
          </View>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Authentication configuration</Text>
          <Text style={styles.feature}>• Session management</Text>
          <Text style={styles.feature}>• IP whitelisting/blacklisting</Text>
          <Text style={styles.feature}>• Security monitoring</Text>
          <Text style={styles.feature}>• Threat detection systems</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Authentication Settings → Configuration for 2FA, password policies
          {
            '\n'}
          • Session Controls → Session timeout and concurrency settings
          {
            '\n'}
          • Access Controls → IP and geographic restrictions management
          {
            '\n'}
          • Monitoring → Security event tracking and alert configuration
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  featureInfo: { flex: 1, marginRight: 16 },
  featureName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  featureDescription: { fontSize: 12, color: '#7f8c8d', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});