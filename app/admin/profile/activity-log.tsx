import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function ActivityLog() {
  const activityLog = [
    { id: 'al001', action: 'Banned User', details: 'Banned user002 for spamming', timestamp: '2024-01-18 10:30:00' },
    { id: 'al002', action: 'Changed Policy', details: 'Updated hate speech policy', timestamp: '2024-01-18 09:00:00' },
    { id: 'al003', action: 'Logged In', details: 'Logged in from 127.0.0.1', timestamp: '2024-01-18 08:00:00' },
  ];

  const activityCategories = [
    { name: 'Moderation Actions', description: 'User bans, content removal, warnings' },
    { name: 'System Changes', description: 'Configuration updates, feature toggles' },
    { name: 'User Management', description: 'Account modifications, role changes' },
    { name: 'Security Events', description: 'Login attempts, password changes' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Activity Log</Text>
        <Text style={styles.subtitle}>Detailed History of Administrative Actions</Text>
        <Link href="/admin/profile" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Profile</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a detailed history of administrative actions and system access.
          It includes tools for searching, filtering, and exporting the activity log.
        </Text>

        <Text style={styles.sectionTitle}>Activity Log:</Text>
        {activityLog.map((log) => (
          <View key={log.id} style={styles.logCard}>
            <Text style={styles.logAction}>{log.action}</Text>
            <Text style={styles.logDetails}>{log.details}</Text>
            <Text style={styles.logTimestamp}>{log.timestamp}</Text>
          </View>
        ))}

        <Text style={styles.sectionTitle}>Activity Categories:</Text>
        <View style={styles.categoriesContainer}>
          {activityCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Comprehensive action logging</Text>
          <Text style={styles.feature}>• Search and filtering capabilities</Text>
          <Text style={styles.feature}>• Export functionality</Text>
          <Text style={styles.feature}>• Audit trail maintenance</Text>
          <Text style={styles.feature}>• Performance tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Action Details → Detailed action information with context
          {
            '\n'}
          • Search and Filter → Activity search interface with date and user filters
          {
            '\n'}
          • Export Options → Activity log export tools for PDF and CSV
          {
            '\n'}
          • Audit Reports → Comprehensive audit reporting and analysis
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#27ae60', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  logCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  logAction: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  logDetails: { fontSize: 14, color: '#555', marginVertical: 4 },
  logTimestamp: { fontSize: 12, color: '#7f8c8d' },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});