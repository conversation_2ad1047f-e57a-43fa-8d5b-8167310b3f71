import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Switch } from 'react-native';
import { Link } from 'expo-router';
import { useState } from 'react';

export default function ProfileSecurity() {
  const [twoFactorAuth, setTwoFactorAuth] = useState(true);

  const securityFeatures = [
    { name: 'Password Security', description: 'Strength requirements and change history' },
    { name: 'Two-Factor Authentication', description: 'Setup and backup codes' },
    { name: 'Login Monitoring', description: 'Recent login attempts and locations' },
    { name: 'Device Management', description: 'Trusted device registration' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Security Settings</Text>
        <Text style={styles.subtitle}>Account Security Configuration and Monitoring</Text>
        <Link href="/admin/profile" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Profile</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing account security settings, including password management,
          two-factor authentication, login history, and device management.
        </Text>

        <Text style={styles.sectionTitle}>Security Features:</Text>
        <View style={styles.featuresContainer}>
          {securityFeatures.map((feature, index) => (
            <TouchableOpacity key={index} style={styles.featureCard}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.featureCard}>
            <View style={styles.featureInfo}>
              <Text style={styles.featureName}>Two-Factor Authentication</Text>
              <Text style={styles.featureDescription}>Enable or disable 2FA for your account</Text>
            </View>
            <Switch
              value={twoFactorAuth}
              onValueChange={setTwoFactorAuth}
              trackColor={{ false: '#ddd', true: '#27ae60' }}
              thumbColor={twoFactorAuth ? '#fff' : '#f4f3f4'}
            />
          </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Password management</Text>
          <Text style={styles.feature}>• Two-factor authentication setup</Text>
          <Text style={styles.feature}>• Login history tracking</Text>
          <Text style={styles.feature}>• Security alert configuration</Text>
          <Text style={styles.feature}>• Device management</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Password Change → Password Change Screen
          {
            '\n'}
          • 2FA Setup → Two-Factor Authentication Setup Screen
          {
            '\n'}
          • Login History → Detailed login tracking and session management
          {
            '\n'}
          • Device Management → Trusted device configuration and management
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  featuresContainer: { marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  featureInfo: { flex: 1, marginRight: 16 },
  featureName: { fontSize: 16, fontWeight: 'bold', color: '#e74c3c' },
  featureDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});