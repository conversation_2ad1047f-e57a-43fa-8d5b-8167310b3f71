import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function SessionManagement() {
  const activeSessions = [
    { id: 'ses001', device: 'Chrome on macOS', ip: '*************', location: 'New York, USA', lastActivity: '2 minutes ago' },
    { id: 'ses002', device: 'Meena App on iPhone 15', ip: '********', location: 'San Francisco, USA', lastActivity: '1 hour ago' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Session Management</Text>
        <Text style={styles.subtitle}>Active Session Monitoring and Control</Text>
        <Link href="/admin/profile" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Profile</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for monitoring and controlling active sessions. It includes tools for
          terminating sessions, tracking login locations, and monitoring device information.
        </Text>

        <Text style={styles.sectionTitle}>Active Sessions:</Text>
        {activeSessions.map((session) => (
          <View key={session.id} style={styles.sessionCard}>
            <Text style={styles.sessionDevice}>{session.device}</Text>
            <Text style={styles.sessionDetails}>IP: {session.ip} | Location: {session.location}</Text>
            <Text style={styles.sessionActivity}>Last Activity: {session.lastActivity}</Text>
            <TouchableOpacity style={styles.terminateButton}>
              <Text style={styles.terminateButtonText}>Terminate Session</Text>
            </TouchableOpacity>
          </View>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Active session display</Text>
          <Text style={styles.feature}>• Session termination controls</Text>
          <Text style={styles.feature}>• Login location tracking</Text>
          <Text style={styles.feature}>• Device information monitoring</Text>
          <Text style={styles.feature}>• Security alert configuration</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Session Control → Individual session management with detailed logs
          
          • Security Monitoring → Session security tracking and anomaly detection
          
          • Device Information → Detailed device analysis and history
          
          • Alert Configuration → Security alert setup for suspicious activities
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  sessionCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  sessionDevice: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  sessionDetails: { fontSize: 12, color: '#555', marginVertical: 4 },
  sessionActivity: { fontSize: 12, color: '#7f8c8d' },
  terminateButton: { backgroundColor: '#e74c3c', padding: 8, borderRadius: 4, alignSelf: 'flex-start', marginTop: 8 },
  terminateButtonText: { color: '#fff', fontSize: 12, fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});
