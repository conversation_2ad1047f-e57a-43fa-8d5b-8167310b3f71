import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { Link } from 'expo-router';

export default function ProfileSettings() {
  const settingsCategories = [
    { name: 'Personal Information', description: 'Name, email, phone, department' },
    { name: 'Preferences', description: 'Language, timezone, notification settings' },
    { name: 'Contact Details', description: 'Emergency contact, backup email' },
    { name: 'Display Settings', description: 'Theme, layout preferences' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Profile Settings</Text>
        <Text style={styles.subtitle}>Personal Information and Contact Details</Text>
        <Link href="/admin/profile" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Profile</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing personal information and contact details. It includes
          tools for editing personal information, managing notification preferences, and customizing
          display settings.
        </Text>

        <Text style={styles.sectionTitle}>Settings Categories:</Text>
        <View style={styles.categoriesContainer}>
          {settingsCategories.map((cat, index) => (
            <TouchableOpacity key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Edit Personal Information:</Text>
        <View style={styles.formContainer}>
            <TextInput style={styles.input} placeholder="Full Name" />
            <TextInput style={styles.input} placeholder="Email Address" />
            <TextInput style={styles.input} placeholder="Phone Number" />
            <TouchableOpacity style={styles.saveButton}>
                <Text style={styles.saveButtonText}>Save Changes</Text>
            </TouchableOpacity>
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Personal information editing</Text>
          <Text style={styles.feature}>• Contact details management</Text>
          <Text style={styles.feature}>• Notification preferences</Text>
          <Text style={styles.feature}>• Language and timezone settings</Text>
          <Text style={styles.feature}>• Profile photo management</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Information Editor → Personal details modification form
          {
            '\n'}
          • Notification Settings → Notification preference management
          {
            '\n'}
          • Contact Management → Contact information updates form
          {
            '\n'}
          • Display Preferences → Interface customization options
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 16, fontWeight: 'bold', color: '#3498db' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  formContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  input: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 12, marginBottom: 12, backgroundColor: '#f8f9fa' },
  saveButton: { backgroundColor: '#27ae60', padding: 12, borderRadius: 6, alignItems: 'center' },
  saveButtonText: { color: '#fff', fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});