import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function PaymentDashboard() {
  const keyMetrics = [
    { label: 'Monthly Revenue', value: '€125,000', color: '#27ae60' },
    { label: 'Transaction Volume', value: '12,345', color: '#3498db' },
    { label: 'Gold Memberships', value: '5,432', color: '#f1c40f' },
    { label: 'Fraud Alerts', value: '23', color: '#e74c3c' },
  ];

  const paymentCategories = [
    { name: 'Gold Membership', description: 'Premium subscription payments', price: '80,00€ (Lifetime)' },
    { name: 'Group Creation', description: 'Fees for creating new groups', price: 'Variable' },
    { name: 'Channel Creation', description: 'Fees for creating new channels', price: 'Variable' },
    { name: 'Verification Services', description: 'Payment for verification services', price: '14,49€ - 40,49€' },
  ];

  const verificationPricing = [
    { type: 'Private Profile Verification', price: '14,49€', description: 'Basic identity confirmation' },
    { type: 'Public Page Verification', price: '24,49€', description: 'Enhanced verification with public badge' },
    { type: 'Both Verifications Bundle', price: '40,49€', description: 'Private + Public verification combined' },
    { type: 'Business Verification', price: 'Variable', description: 'Business account verification with separate pricing' },
  ];

  const mainTasks = [
      { 
        title: 'Transaction History', 
        route: '/admin/payments/transactions', 
        color: '#3498db',
        description: 'View and search all transactions'
    }, { 
        title: 'Payment Verification', 
        route: '/admin/payments/verification', 
        color: '#27ae60',
        description: 'Verify and confirm payments'
    }, { 
        title: 'Refund Management', 
        route: '/admin/payments/refunds', 
        color: '#e67e22',
        description: 'Manage and process refund requests'
    }, { 
        title: 'Subscription Management', 
        route: '/admin/payments/subscriptions', 
        color: '#9b59b6',
        description: 'Manage Gold membership subscriptions'
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Payment Dashboard</Text>
        <Text style={styles.subtitle}>Transaction Overview and Revenue Statistics</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This dashboard provides a comprehensive overview of all payment-related activities,
          including revenue statistics, transaction volumes, and fraud detection alerts.
        </Text>

        <Text style={styles.sectionTitle}>Key Metrics:</Text>
        <View style={styles.metricsGrid}>
          {keyMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Main Tasks:</Text>
        {mainTasks.map((task, index) => (
          <Link key={index} href={task.route} asChild>
            <TouchableOpacity style={[styles.taskCard, { borderLeftColor: task.color }]}>
                <Text style={[styles.taskTitle, { color: task.color }]}>{task.title}</Text>
                <Text style={styles.taskDescription}>{task.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Payment Categories:</Text>
        <View style={styles.typesContainer}>
          {paymentCategories.map((type, index) => (
            <View key={index} style={styles.typeCard}>
              <View style={styles.typeHeader}>
                <Text style={styles.typeName}>{type.name}</Text>
                <Text style={styles.typePrice}>{type.price}</Text>
              </View>
              <Text style={styles.typeDescription}>{type.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Verification Pricing (European Format):</Text>
        <View style={styles.pricingContainer}>
          {verificationPricing.map((item, index) => (
            <View key={index} style={styles.pricingCard}>
              <View style={styles.pricingHeader}>
                <Text style={styles.pricingType}>{item.type}</Text>
                <Text style={styles.pricingAmount}>{item.price}</Text>
              </View>
              <Text style={styles.pricingDescription}>{item.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Revenue analytics and trends</Text>
          <Text style={styles.feature}>• Transaction volume tracking</Text>
          <Text style={styles.feature}>• Payment method distribution</Text>
          <Text style={styles.feature}>• Fraud detection alerts</Text>
          <Text style={styles.feature}>• Financial reporting tools</Text>
        </View>

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f39c12', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, borderTopWidth: 4, alignItems: 'center' },
  metricValue: { fontSize: 22, fontWeight: 'bold', color: '#2c3e50' },
  metricLabel: { fontSize: 12, color: '#7f8c8d', textAlign: 'center', marginTop: 4 },
  taskCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4 },
  taskTitle: { fontSize: 18, fontWeight: 'bold' },
  taskDescription: { fontSize: 14, color: '#555', marginTop: 4 },
  typesContainer: { marginBottom: 16 },
  typeCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  typeHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 },
  typeName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  typePrice: { fontSize: 14, fontWeight: 'bold', color: '#27ae60' },
  typeDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  pricingContainer: { marginBottom: 16 },
  pricingCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4, borderLeftColor: '#3498db' },
  pricingHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 },
  pricingType: { fontSize: 16, fontWeight: 'bold', color: '#3498db', flex: 1 },
  pricingAmount: { fontSize: 18, fontWeight: 'bold', color: '#27ae60' },
  pricingDescription: { fontSize: 14, color: '#555', lineHeight: 20 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
});