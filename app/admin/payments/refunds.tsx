import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RefundManagement() {
  const refundRequests = [
    {
      id: 'ref001',
      user: 'user003',
      amount: '€49.99',
      reason: 'Service Issue',
      status: 'Pending',
      date: '2024-01-18',
    },
    {
      id: 'ref002',
      user: 'user008',
      amount: '€99.99',
      reason: 'Unauthorized Charge',
      status: 'Approved',
      date: '2024-01-17',
    },
    {
      id: 'ref003',
      user: 'user009',
      amount: '€19.99',
      reason: 'Customer Satisfaction',
      status: 'Pending',
      date: '2024-01-16',
    },
  ];

  const refundCategories = [
    { name: 'Service Issues', description: 'Technical problems or service failures' },
    { name: 'Unauthorized Charges', description: 'Fraudulent or disputed transactions' },
    { name: 'Policy Violations', description: 'Refunds due to policy breaches' },
    { name: 'Customer Satisfaction', description: 'Goodwill refunds' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Refund Management</Text>
        <Text style={styles.subtitle}>Manage Refund Requests</Text>
        <Link href="/admin/payments/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing refund requests, with tools for status tracking, customer
          communication, and financial impact analysis.
        </Text>

        <Text style={styles.sectionTitle}>Pending Refund Requests:</Text>
        {refundRequests.map((req) => (
          <TouchableOpacity key={req.id} style={styles.requestCard}>
            <View style={styles.requestHeader}>
              <Text style={styles.requestUser}>{req.user}</Text>
              <Text style={styles.requestAmount}>{req.amount}</Text>
            </View>
            <Text style={styles.requestDetails}>Reason: {req.reason}</Text>
            <Text style={styles.requestDetails}>Status: {req.status}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Refund Categories:</Text>
        <View style={styles.categoriesContainer}>
          {refundCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Refund request processing</Text>
          <Text style={styles.feature}>• Status tracking system</Text>
          <Text style={styles.feature}>• Customer communication tools</Text>
          <Text style={styles.feature}>• Refund policy enforcement</Text>
          <Text style={styles.feature}>• Financial impact analysis</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Refund Processing → Refund approval workflow with reason codes
          {
            '\n'}
          • Customer Communication → User notification system for status updates
          {
            '\n'}
          • Financial Reporting → Refund impact analysis and reporting tools
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e74c3c', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  requestCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  requestHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  requestUser: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  requestAmount: { fontSize: 16, fontWeight: 'bold', color: '#e74c3c' },
  requestDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});