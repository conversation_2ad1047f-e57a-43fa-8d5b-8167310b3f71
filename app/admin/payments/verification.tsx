import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function PaymentVerification() {
  const verificationQueue = [
    {
      id: 'pv001',
      user: 'user005',
      amount: '€19.99',
      method: 'Card',
      risk: 'High',
      date: '2024-01-18',
    },
    {
      id: 'pv002',
      user: 'user006',
      amount: '€49.99',
      method: 'PayPal',
      risk: 'Medium',
      date: '2024-01-17',
    },
    {
      id: 'pv003',
      user: 'user007',
      amount: '€9.99',
      method: 'Card',
      risk: 'Low',
      date: '2024-01-16',
    },
  ];

  const verificationProcess = [
    { step: 1, name: 'Payment Submission', description: 'User initiates payment' },
    { step: 2, name: 'Automated Checks', description: 'Fraud detection algorithms' },
    { step: 3, name: 'Risk Assessment', description: 'AI-powered risk scoring' },
    { step: 4, name: 'Manual Review', description: 'Human verification if needed' },
    { step: 5, name: 'Payment Confirmation', description: 'Final approval/rejection' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Payment Verification</Text>
        <Text style={styles.subtitle}>Payment Confirmation and Fraud Detection</Text>
        <Link href="/admin/payments/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides an interface for payment confirmation, with fraud detection
          indicators and a clear workflow for verifying transactions.
        </Text>

        <Text style={styles.sectionTitle}>Verification Queue:</Text>
        {verificationQueue.map((item) => (
          <TouchableOpacity key={item.id} style={styles.verificationCard}>
            <View style={styles.verificationHeader}>
              <Text style={styles.verificationId}>{item.id}</Text>
              <Text style={[styles.risk, { color: item.risk === 'High' ? '#e74c3c' : item.risk === 'Medium' ? '#f39c12' : '#27ae60' }]}>
                {item.risk} Risk
              </Text>
            </View>
            <Text style={styles.verificationDetails}>User: {item.user} | Amount: {item.amount}</Text>
            <Text style={styles.verificationDetails}>Method: {item.method} | Date: {item.date}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Verification Process:</Text>
        <View style={styles.processContainer}>
          {verificationProcess.map((item) => (
            <View key={item.step} style={styles.processStep}>
              <Text style={styles.stepNumber}>{item.step}</Text>
              <View style={styles.stepInfo}>
                <Text style={styles.stepName}>{item.name}</Text>
                <Text style={styles.stepDescription}>{item.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Payment verification workflow</Text>
          <Text style={styles.feature}>• Fraud detection indicators</Text>
          <Text style={styles.feature}>• Risk assessment scoring</Text>
          <Text style={styles.feature}>• Manual review capabilities</Text>
          <Text style={styles.feature}>• Verification history tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Payment Details → Detailed payment information and transaction history
          {
            '\n'}
          • Fraud Investigation → Fraud analysis tools and risk scoring details
          {
            '\n'}
          • User Verification → Customer identity confirmation and account history
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#27ae60', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  verificationCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  verificationHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  verificationId: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  risk: { fontSize: 14, fontWeight: 'bold' },
  verificationDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  processContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  processStep: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  stepNumber: { fontSize: 18, fontWeight: 'bold', color: '#27ae60', marginRight: 12 },
  stepInfo: { flex: 1 },
  stepName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  stepDescription: { fontSize: 12, color: '#555' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});