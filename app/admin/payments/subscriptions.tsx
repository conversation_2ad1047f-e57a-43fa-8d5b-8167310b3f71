import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function SubscriptionManagement() {
  const subscriptions = [
    {
      id: 'sub001',
      user: 'user001',
      plan: 'Gold Annual',
      status: 'Active',
      renewalDate: '2025-01-18',
    },
    {
      id: 'sub002',
      user: 'user010',
      plan: 'Gold Monthly',
      status: 'Canceled',
      renewalDate: 'N/A',
    },
    {
      id: 'sub003',
      user: 'user011',
      plan: 'Gold Annual',
      status: 'Active',
      renewalDate: '2025-02-15',
    },
  ];

  const subscriptionFeatures = [
    { name: 'Active Subscriptions', description: 'Currently paying members' },
    { name: 'Renewal Tracking', description: 'Upcoming renewal dates' },
    { name: 'Cancellation Management', description: 'Subscription termination' },
    { name: 'Revenue Analytics', description: 'Subscription revenue trends' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Subscription Management</Text>
        <Text style={styles.subtitle}>Gold Membership Tracking and Renewals</Text>
        <Link href="/admin/payments/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing Gold membership subscriptions, including renewal tracking,
          cancellation processing, and revenue forecasting.
        </Text>

        <Text style={styles.sectionTitle}>Active Subscriptions:</Text>
        {subscriptions.map((sub) => (
          <TouchableOpacity key={sub.id} style={styles.subscriptionCard}>
            <View style={styles.subscriptionHeader}>
              <Text style={styles.subscriptionUser}>{sub.user}</Text>
              <Text style={[styles.status, { color: sub.status === 'Active' ? '#27ae60' : '#e74c3c' }]}>
                {sub.status}
              </Text>
            </View>
            <Text style={styles.subscriptionDetails}>Plan: {sub.plan}</Text>
            <Text style={styles.subscriptionDetails}>Renewal Date: {sub.renewalDate}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Subscription Features:</Text>
        <View style={styles.featuresContainer}>
          {subscriptionFeatures.map((feature, index) => (
            <View key={index} style={styles.featureCard}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Subscription lifecycle management</Text>
          <Text style={styles.feature}>• Renewal tracking and notifications</Text>
          <Text style={styles.feature}>• Cancellation processing</Text>
          <Text style={styles.feature}>• Revenue forecasting</Text>
          <Text style={styles.feature}>• Customer retention analytics</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Subscription Details → Individual subscription management with payment history
          {
            '\n'}
          • Renewal Processing → Automatic renewal system and notifications
          {
            '\n'}
          • Cancellation Workflow → Subscription termination process with feedback form
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#f1c40f', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#2c3e50', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(44, 62, 80, 0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  subscriptionCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  subscriptionHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  subscriptionUser: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  status: { fontSize: 14, fontWeight: 'bold' },
  subscriptionDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  featuresContainer: { marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  featureName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  featureDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});