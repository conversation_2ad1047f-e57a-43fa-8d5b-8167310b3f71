import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link, useLocalSearchParams } from 'expo-router';

export default function TransactionDetail() {
  const { id } = useLocalSearchParams();

  const transactionData = {
    id: id as string,
    user: 'user001',
    amount: '€99.99',
    method: 'Card',
    status: 'Completed',
    date: '2024-01-18',
    fraudScore: 0.1,
  };

  const transactionInfo = [
    { label: 'Payment Details', value: `${transactionData.amount} via ${transactionData.method}` },
    { label: 'User Information', value: `User ID: ${transactionData.user}` },
    { label: 'Fraud Analysis', value: `Risk Score: ${transactionData.fraudScore}` },
    { label: 'Status Tracking', value: `Status: ${transactionData.status}` },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Transaction Detail</Text>
        <Text style={styles.subtitle}>Transaction ID: {transactionData.id}</Text>
        <Link href="/admin/payments/transactions" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to History</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a detailed analysis of a single transaction, including fraud
          detection, user payment history, and dispute management tools.
        </Text>

        <Text style={styles.sectionTitle}>Transaction Information:</Text>
        <View style={styles.infoCard}>
          {transactionInfo.map((info, index) => (
            <View key={index} style={styles.infoItem}>
              <Text style={styles.infoLabel}>{info.label}:</Text>
              <Text style={styles.infoValue}>{info.value}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Complete transaction information</Text>
          <Text style={styles.feature}>• Payment method details</Text>
          <Text style={styles.feature}>• Fraud risk assessment</Text>
          <Text style={styles.feature}>• User payment history</Text>
          <Text style={styles.feature}>• Dispute management tools</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationSection}>
            <Link href={`/admin/users/${transactionData.user}`} asChild>
                <TouchableOpacity style={styles.navButton}>
                    <Text style={styles.navButtonText}>View User Profile</Text>
                </TouchableOpacity>
            </Link>
            <Link href={`/admin/payments/refunds`} asChild>
                <TouchableOpacity style={styles.navButton}>
                    <Text style={styles.navButtonText}>Process Refund</Text>
                </TouchableOpacity>
            </Link>
            <TouchableOpacity style={styles.navButton}>
                <Text style={styles.navButtonText}>Investigate Fraud</Text>
            </TouchableOpacity>
        </View>

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  infoCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  infoItem: { flexDirection: 'row', justifyContent: 'space-between', paddingVertical: 8, borderBottomWidth: 1, borderBottomColor: '#ecf0f1' },
  infoLabel: { fontSize: 14, color: '#555', fontWeight: 'bold' },
  infoValue: { fontSize: 14, color: '#2c3e50' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  navigationSection: { marginTop: 24 },
  navButton: { backgroundColor: '#95a5a6', padding: 12, borderRadius: 6, alignItems: 'center', marginBottom: 8 },
  navButtonText: { color: '#fff', fontSize: 14 },
});