import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function TransactionHistory() {
  const transactions = [
    {
      id: 'txn_1',
      user: 'user001',
      amount: '80,00€',
      method: 'Card',
      status: 'Completed',
      date: '2024-01-18',
      type: 'Gold Membership',
    },
    {
      id: 'txn_2',
      user: 'user002',
      amount: '24,49€',
      method: 'PayPal',
      status: 'Completed',
      date: '2024-01-17',
      type: 'Public Page Verification',
    },
    {
      id: 'txn_3',
      user: 'user003',
      amount: '40,49€',
      method: 'Card',
      status: 'Refunded',
      date: '2024-01-16',
      type: 'Both Verifications',
    },
    {
      id: 'txn_4',
      user: 'user004',
      amount: '14,49€',
      method: 'Bank Transfer',
      status: 'Pending',
      date: '2024-01-15',
      type: 'Private Profile Verification',
    },
  ];

  const searchFilters = [
    { name: 'Date Range', options: [] },
    { name: 'Amount Range', options: [] },
    { name: 'Payment Method', options: ['Card', 'PayPal', 'Bank Transfer'] },
    { name: 'Status', options: ['Completed', 'Pending', 'Failed', 'Refunded'] },
    { name: 'User', options: [] },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Transaction History</Text>
        <Text style={styles.subtitle}>Searchable Transaction Database</Text>
        <Link href="/admin/payments/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a searchable database of all transactions, with advanced filtering,
          export capabilities, and bulk action operations for efficient management.
        </Text>

        <View style={styles.searchBox}>
          <Text style={styles.searchPlaceholder}>Search by transaction ID, user ID, or email...</Text>
        </View>

        <Text style={styles.sectionTitle}>Search Filters:</Text>
        <View style={styles.filtersContainer}>
          {searchFilters.map((filter, index) => (
            <TouchableOpacity key={index} style={styles.filterButton}>
              <Text style={styles.filterText}>{filter.name}: All</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Transaction History:</Text>
        {transactions.map((txn) => (
          <Link key={txn.id} href={`/admin/payments/transactions/${txn.id}`} asChild>
            <TouchableOpacity style={styles.transactionCard}>
              <View style={styles.transactionHeader}>
                <Text style={styles.transactionId}>{txn.id}</Text>
                <Text style={styles.transactionAmount}>{txn.amount}</Text>
              </View>
              <Text style={styles.transactionDetails}>User: {txn.user} | Method: {txn.method}</Text>
              <View style={styles.transactionMeta}>
                <Text style={styles.metaText}>Date: {txn.date}</Text>
                <Text style={[styles.statusText, { color: txn.status === 'Completed' ? '#27ae60' : txn.status === 'Refunded' ? '#e67e22' : '#e74c3c' }]}>
                  {txn.status}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Comprehensive transaction search</Text>
          <Text style={styles.feature}>• Advanced filtering options</Text>
          <Text style={styles.feature}>• Export capabilities</Text>
          <Text style={styles.feature}>• Bulk action operations</Text>
          <Text style={styles.feature}>• Transaction status tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Transaction Detail → Individual transaction screen with fraud analysis
          {
            '\n'}
          • User Profile → Customer detail screen with payment history
          {
            '\n'}
          • Refund Process → Refund management interface for processing refunds
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#3498db', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  searchBox: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 16, marginBottom: 16, backgroundColor: '#fff' },
  searchPlaceholder: { color: '#999', fontSize: 14 },
  filtersContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  filterButton: { backgroundColor: '#fff', padding: 10, borderRadius: 20, marginRight: 8, marginBottom: 8 },
  filterText: { fontSize: 12, color: '#555' },
  transactionCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  transactionHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  transactionId: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  transactionAmount: { fontSize: 16, fontWeight: 'bold', color: '#27ae60' },
  transactionDetails: { fontSize: 12, color: '#555', marginBottom: 8 },
  transactionMeta: { flexDirection: 'row', justifyContent: 'space-between' },
  metaText: { fontSize: 12, color: '#7f8c8d' },
  statusText: { fontSize: 12, fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});