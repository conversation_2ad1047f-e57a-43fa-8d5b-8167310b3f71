import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RemoteWipeIndex() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Remote Wipe Management</Text>
        <Text style={styles.subtitle}>Manage remote profile deletion requests</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>System Overview:</Text>
        <Text style={styles.description}>
          The Remote Wipe Management System handles requests for remote profile deletion, 
          including PIN verification, secret phrase verification using African languages, 
          and secure audit logging for all remote wipe requests.
        </Text>

        <Text style={styles.sectionTitle}>Available Modules:</Text>
        
        <Link href="/admin/remote-wipe/requests" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Remote Wipe Requests</Text>
            <Text style={styles.moduleDescription}>
              View and manage remote wipe requests with verification status
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/remote-wipe/pin-verification" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>PIN Verification</Text>
            <Text style={styles.moduleDescription}>
              Verify PIN for remote wipe requests with fraud detection
            </Text>
          </TouchableOpacity>
        </Link>

        <Link href="/admin/remote-wipe/secret-phrase-verification" asChild>
          <TouchableOpacity style={styles.moduleCard}>
            <Text style={styles.moduleName}>Secret Phrase Verification</Text>
            <Text style={styles.moduleDescription}>
              Verify secret phrases in African languages for account recovery
            </Text>
          </TouchableOpacity>
        </Link>

        <Text style={styles.sectionTitle}>Supported African Languages:</Text>
        <View style={styles.languageList}>
          <Text style={styles.language}>• Swahili</Text>
          <Text style={styles.language}>• Zulu</Text>
          <Text style={styles.language}>• Kikongo</Text>
          <Text style={styles.language}>• Shona</Text>
          <Text style={styles.language}>• Yoruba</Text>
          <Text style={styles.language}>• Fon</Text>
          <Text style={styles.language}>• Bambara</Text>
          <Text style={styles.language}>• Sango</Text>
        </View>

        <Text style={styles.sectionTitle}>Key Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• PIN creation interface for remote wipe</Text>
          <Text style={styles.feature}>• Old account ID input for remote wipe</Text>
          <Text style={styles.feature}>• Secret phrase verification using specified African languages</Text>
          <Text style={styles.feature}>• Confirmation with PIN for remote wipe</Text>
          <Text style={styles.feature}>• Audit logging for all remote wipe requests</Text>
          <Text style={styles.feature}>• Fraud detection indicators</Text>
        </View>

        <Text style={styles.sectionTitle}>Security Requirements:</Text>
        <View style={styles.securityList}>
          <Text style={styles.security}>• Two-factor verification (PIN + Secret Phrase)</Text>
          <Text style={styles.security}>• Account ID verification</Text>
          <Text style={styles.security}>• Comprehensive audit trail</Text>
          <Text style={styles.security}>• Fraud detection and prevention</Text>
          <Text style={styles.security}>• Secure session management</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#c0392b',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  moduleCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#c0392b',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  moduleName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#c0392b',
    marginBottom: 8,
  },
  moduleDescription: {
    fontSize: 14,
    color: '#555',
    lineHeight: 20,
  },
  languageList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  language: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    marginRight: 16,
    width: '45%',
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  securityList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
  },
  security: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
});
