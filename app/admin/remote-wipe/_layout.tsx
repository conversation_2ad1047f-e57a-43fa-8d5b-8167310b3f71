import { Stack } from 'expo-router';

export default function RemoteWipeLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="index" 
        options={{ 
          title: 'Remote Wipe Management',
          headerStyle: { backgroundColor: '#c0392b' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="requests" 
        options={{ 
          title: 'Remote Wipe Requests',
          headerStyle: { backgroundColor: '#c0392b' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="pin-verification" 
        options={{ 
          title: 'PIN Verification',
          headerStyle: { backgroundColor: '#c0392b' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="secret-phrase-verification" 
        options={{ 
          title: 'Secret Phrase Verification',
          headerStyle: { backgroundColor: '#c0392b' },
          headerTintColor: '#fff',
        }} 
      />
      <Stack.Screen 
        name="[id]" 
        options={{ 
          title: 'Remote Wipe Request Details',
          headerStyle: { backgroundColor: '#c0392b' },
          headerTintColor: '#fff',
        }} 
      />
    </Stack>
  );
}
