import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function RemoteWipeRequests() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Remote Wipe Requests</Text>
        <Text style={styles.subtitle}>Manage remote profile deletion requests</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Manage remote wipe requests with verification status. Handle PIN verification, 
          secret phrase verification using African languages, and secure audit logging 
          for all remote wipe requests.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Remote wipe request listing</Text>
          <Text style={styles.component}>• Verification status</Text>
          <Text style={styles.component}>• Filter options (status, date)</Text>
          <Text style={styles.component}>• Request details preview</Text>
        </View>

        <Text style={styles.sectionTitle}>Request Detail Components:</Text>
        <View style={styles.detailComponents}>
          <Text style={styles.component}>• Complete request information (Old account ID, New account ID)</Text>
          <Text style={styles.component}>• Timestamp and verification status</Text>
          <Text style={styles.component}>• PIN verification indicators</Text>
          <Text style={styles.component}>• Secret phrase verification indicators</Text>
          <Text style={styles.component}>• Action options (Confirm wipe, Reject request, Request additional verification)</Text>
        </View>

        <Text style={styles.sectionTitle}>Verification Process:</Text>
        <View style={styles.processSteps}>
          <Text style={styles.step}>1. User submits remote wipe request</Text>
          <Text style={styles.step}>2. System validates old account ID</Text>
          <Text style={styles.step}>3. PIN verification required</Text>
          <Text style={styles.step}>4. Secret phrase verification in African language</Text>
          <Text style={styles.step}>5. Admin review and approval</Text>
          <Text style={styles.step}>6. Remote wipe execution with audit logging</Text>
        </View>

        <Text style={styles.sectionTitle}>Supported African Languages:</Text>
        <View style={styles.languageGrid}>
          <Text style={styles.language}>🇹🇿 Swahili</Text>
          <Text style={styles.language}>🇿🇦 Zulu</Text>
          <Text style={styles.language}>🇨🇩 Kikongo</Text>
          <Text style={styles.language}>🇿🇼 Shona</Text>
          <Text style={styles.language}>🇳🇬 Yoruba</Text>
          <Text style={styles.language}>🇧🇯 Fon</Text>
          <Text style={styles.language}>🇲🇱 Bambara</Text>
          <Text style={styles.language}>🇨🇫 Sango</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Request selection → Remote Wipe Request Detail Screen</Text>
          <Text style={styles.navItem}>• PIN verification → PIN Verification Screen</Text>
          <Text style={styles.navItem}>• Secret phrase verification → Secret Phrase Verification Screen</Text>
          <Text style={styles.navItem}>• Account restoration → Account Restoration Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Security Features:</Text>
        <View style={styles.securityFeatures}>
          <Text style={styles.feature}>• Two-factor verification (PIN + Secret Phrase)</Text>
          <Text style={styles.feature}>• Account ID cross-verification</Text>
          <Text style={styles.feature}>• Fraud detection indicators</Text>
          <Text style={styles.feature}>• Comprehensive audit trail</Text>
          <Text style={styles.feature}>• Session timeout protection</Text>
          <Text style={styles.feature}>• Encrypted communication channels</Text>
        </View>

        <Text style={styles.sectionTitle}>Request Status Types:</Text>
        <View style={styles.statusList}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#f39c12' }]} />
            <Text style={styles.statusText}>Pending: Awaiting verification</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#3498db' }]} />
            <Text style={styles.statusText}>In Progress: Verification in process</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#27ae60' }]} />
            <Text style={styles.statusText}>Approved: Ready for execution</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#e74c3c' }]} />
            <Text style={styles.statusText}>Rejected: Request denied</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Pending Requests</Text>
            <Text style={styles.actionDescription}>View requests awaiting verification</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>PIN Verification</Text>
            <Text style={styles.actionDescription}>Process PIN verifications</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Phrase Verification</Text>
            <Text style={styles.actionDescription}>Verify secret phrases</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Audit Log</Text>
            <Text style={styles.actionDescription}>View security audit trail</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#c0392b',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  detailComponents: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  processSteps: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  step: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  languageGrid: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  language: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    width: '48%',
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  securityFeatures: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  statusList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#c0392b',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#c0392b',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
});
