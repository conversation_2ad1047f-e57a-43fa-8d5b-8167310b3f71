import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function SecretGroupMonitoring() {
  const monitoredGroups = [
    {
      id: 'sg001',
      name: 'Project Omega',
      risk: 'High',
      activity: 'High',
      lastDetection: '2024-01-18',
    },
    {
      id: 'sg002',
      name: 'Private Investments Club',
      risk: 'Medium',
      activity: 'Low',
      lastDetection: '2024-01-15',
    },
    {
      id: 'sg003',
      name: 'Underground Art Scene',
      risk: 'Low',
      activity: 'Medium',
      lastDetection: '2024-01-10',
    },
  ];

  const monitoringFeatures = [
    { name: 'Activity Indicators', description: 'Message frequency, member growth' },
    { name: 'Risk Assessment', description: 'AI-powered risk scoring' },
    { name: 'Content Sampling', description: 'Random content review' },
    { name: 'Pattern Detection', description: 'Suspicious activity identification' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Secret Groups Monitoring</Text>
        <Text style={styles.subtitle}>Enhanced Monitoring of Secret Groups</Text>
        <Link href="/admin/groups/management-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides enhanced monitoring of secret groups, using activity indicators,
          risk assessment algorithms, and content sampling to detect policy violations.
        </Text>

        <Text style={styles.sectionTitle}>Monitored Secret Groups:</Text>
        {monitoredGroups.map((group) => (
          <Link key={group.id} href={`/admin/groups/${group.id}`} asChild>
            <TouchableOpacity style={styles.groupCard}>
              <View style={styles.groupHeader}>
                <Text style={styles.groupName}>{group.name}</Text>
                <Text style={[styles.risk, { color: group.risk === 'High' ? '#e74c3c' : '#f39c12' }]}>
                  {group.risk} Risk
                </Text>
              </View>
              <Text style={styles.groupDetails}>Activity Level: {group.activity}</Text>
              <Text style={styles.groupDetails}>Last Detection: {group.lastDetection}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Monitoring Features:</Text>
        <View style={styles.featuresContainer}>
          {monitoringFeatures.map((feature, index) => (
            <View key={index} style={styles.featureCard}>
              <Text style={styles.featureName}>{feature.name}</Text>
              <Text style={styles.featureDescription}>{feature.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Secret group discovery and tracking</Text>
          <Text style={styles.feature}>• Activity level monitoring</Text>
          <Text style={styles.feature}>• Risk assessment algorithms</Text>
          <Text style={styles.feature}>• Content sampling for review</Text>
          <Text style={styles.feature}>• Escalation procedures</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Group Investigation → Detailed group analysis with content samples
          {
            '\n'}
          • Content Review → Manual content examination of flagged items
          {
            '\n'}
          • Escalation → Law enforcement coordination for illegal activities
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#e67e22', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  groupCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  groupHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  groupName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  risk: { fontSize: 14, fontWeight: 'bold' },
  groupDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  featuresContainer: { marginBottom: 16 },
  featureCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  featureName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  featureDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});