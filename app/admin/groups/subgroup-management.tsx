import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function SubgroupManagement() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Subgroup Management</Text>
        <Text style={styles.subtitle}>Manage subgroups nested under parent groups</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          Manage subgroups that are nested under parent groups. Subgroups are free to create 
          and do not require payment verification, making them accessible for community organization.
        </Text>

        <Text style={styles.sectionTitle}>Components (Placeholder):</Text>
        <View style={styles.componentList}>
          <Text style={styles.component}>• Subgroup listing (nested under parent groups)</Text>
          <Text style={styles.component}>• Parent group indicators</Text>
          <Text style={styles.component}>• Filter options (status, creation date)</Text>
          <Text style={styles.component}>• Subgroup details preview</Text>
          <Text style={styles.component}>• "No payment required" indicator</Text>
        </View>

        <Text style={styles.sectionTitle}>Subgroup Detail Screen Components:</Text>
        <View style={styles.detailComponents}>
          <Text style={styles.component}>• Subgroup information (name, photo, description)</Text>
          <Text style={styles.component}>• Parent group reference</Text>
          <Text style={styles.component}>• Creation date and admin list</Text>
          <Text style={styles.component}>• Member list with roles (Admin, Member)</Text>
          <Text style={styles.component}>• Join date and action options (Remove, Ban)</Text>
          <Text style={styles.component}>• Content preview (recent messages, text only)</Text>
          <Text style={styles.component}>• Highlighted keywords (AI-flagged content)</Text>
        </View>

        <Text style={styles.sectionTitle}>Key Features:</Text>
        <View style={styles.featureList}>
          <Text style={styles.feature}>• No payment required for subgroup creation</Text>
          <Text style={styles.feature}>• Nested hierarchy under parent groups</Text>
          <Text style={styles.feature}>• Inherited permissions from parent group</Text>
          <Text style={styles.feature}>• Content moderation aligned with parent group policies</Text>
          <Text style={styles.feature}>• Member management with role-based access</Text>
          <Text style={styles.feature}>• AI-powered content flagging and review</Text>
        </View>

        <Text style={styles.sectionTitle}>Subgroup Hierarchy:</Text>
        <View style={styles.hierarchyExample}>
          <Text style={styles.hierarchyTitle}>Example Structure:</Text>
          <Text style={styles.hierarchyItem}>📁 Parent Group: "Tech Community"</Text>
          <Text style={styles.hierarchySubitem}>  └── 📂 Subgroup: "Web Development"</Text>
          <Text style={styles.hierarchySubitem}>  └── 📂 Subgroup: "Mobile Apps"</Text>
          <Text style={styles.hierarchySubitem}>  └── 📂 Subgroup: "AI & Machine Learning"</Text>
        </View>

        <Text style={styles.sectionTitle}>Moderation Panel Actions:</Text>
        <View style={styles.moderationActions}>
          <Text style={styles.action}>• "Review Content" button</Text>
          <Text style={styles.action}>• "Ban Subgroup" with reason selection</Text>
          <Text style={styles.action}>• "Warn Creator" (sends in-app notification)</Text>
          <Text style={styles.action}>• Member management (remove, ban users)</Text>
          <Text style={styles.action}>• Content filtering and keyword monitoring</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Options:</Text>
        <View style={styles.navigationList}>
          <Text style={styles.navItem}>• Subgroup selection → Subgroup Detail Screen</Text>
          <Text style={styles.navItem}>• Creation request → Subgroup Creation Request Screen</Text>
          <Text style={styles.navItem}>• Subgroup approval → Subgroup Approval Screen</Text>
          <Text style={styles.navItem}>• Subgroup rejection → Subgroup Rejection Screen</Text>
          <Text style={styles.navItem}>• Back → Subgroup Management Screen</Text>
          <Text style={styles.navItem}>• Content moderation → Content Moderation Screen</Text>
          <Text style={styles.navItem}>• Subgroup action → Subgroup Action Screen</Text>
        </View>

        <Text style={styles.sectionTitle}>Status Indicators:</Text>
        <View style={styles.statusList}>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#27ae60' }]} />
            <Text style={styles.statusText}>Active: Subgroup is operational</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#f39c12' }]} />
            <Text style={styles.statusText}>Under Review: Content being moderated</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusIndicator, { backgroundColor: '#e74c3c' }]} />
            <Text style={styles.statusText}>Banned: Subgroup suspended</Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>Quick Actions:</Text>
        <View style={styles.actionGrid}>
          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>View All</Text>
            <Text style={styles.actionDescription}>All subgroups across parent groups</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Pending Approval</Text>
            <Text style={styles.actionDescription}>Subgroups awaiting approval</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Under Review</Text>
            <Text style={styles.actionDescription}>Subgroups being moderated</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionCard}>
            <Text style={styles.actionTitle}>Parent Groups</Text>
            <Text style={styles.actionDescription}>View parent group hierarchy</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    backgroundColor: '#16a085',
    padding: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#f8f9fa',
  },
  content: {
    padding: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginTop: 24,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#34495e',
    lineHeight: 24,
    marginBottom: 16,
  },
  componentList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  detailComponents: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  component: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  featureList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  feature: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    lineHeight: 20,
  },
  hierarchyExample: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  hierarchyTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 12,
  },
  hierarchyItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
    fontFamily: 'monospace',
  },
  hierarchySubitem: {
    fontSize: 14,
    color: '#777',
    marginBottom: 4,
    fontFamily: 'monospace',
    paddingLeft: 16,
  },
  moderationActions: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  action: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  navigationList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  navItem: {
    fontSize: 14,
    color: '#555',
    marginBottom: 8,
  },
  statusList: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 12,
  },
  statusText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    width: '48%',
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#16a085',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#16a085',
    marginBottom: 4,
  },
  actionDescription: {
    fontSize: 12,
    color: '#555',
  },
});
