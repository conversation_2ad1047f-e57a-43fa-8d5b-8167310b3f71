import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function BannedGroups() {
  const bannedGroups = [
    {
      id: 'bg001',
      name: 'Illegal Content Group',
      reason: 'Illegal Content',
      appeal: 'Denied',
      date: '2023-12-01',
    },
    {
      id: 'bg002',
      name: 'Harassment Campaign',
      reason: 'Harassment',
      appeal: 'Pending',
      date: '2024-01-10',
    },
    {
      id: 'bg003',
      name: 'Spam Central',
      reason: 'Spam',
      appeal: 'None',
      date: '2024-01-15',
    },
  ];

  const banCategories = [
    { name: 'Policy Violations', description: 'Community guideline breaches' },
    { name: 'Illegal Content', description: 'Criminal activity coordination' },
    { name: 'Harassment', description: 'Targeted harassment campaigns' },
    { name: 'Spam', description: 'Excessive promotional content' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Banned Groups</Text>
        <Text style={styles.subtitle}>Manage Banned Groups and Appeals</Text>
        <Link href="/admin/groups/management-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing banned groups, including their appeal status and any unban
          requests. It provides tools for reviewing ban reasons and processing appeals.
        </Text>

        <Text style={styles.sectionTitle}>Banned Groups Directory:</Text>
        {bannedGroups.map((group) => (
          <TouchableOpacity key={group.id} style={styles.groupCard}>
            <View style={styles.groupHeader}>
              <Text style={styles.groupName}>{group.name}</Text>
              <Text style={styles.appealStatus}>{group.appeal}</Text>
            </View>
            <Text style={styles.groupDetails}>Reason: {group.reason}</Text>
            <Text style={styles.groupDetails}>Ban Date: {group.date}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Ban Categories:</Text>
        <View style={styles.categoriesContainer}>
          {banCategories.map((cat, index) => (
            <View key={index} style={styles.categoryCard}>
              <Text style={styles.categoryName}>{cat.name}</Text>
              <Text style={styles.categoryDescription}>{cat.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Banned group directory</Text>
          <Text style={styles.feature}>• Ban reason documentation</Text>
          <Text style={styles.feature}>• Appeal processing system</Text>
          <Text style={styles.feature}>• Unban request management</Text>
          <Text style={styles.feature}>• Ban history tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Ban Details → Specific ban information and group history
          {
            '\n'}
          • Appeal Review → Appeal processing interface with evidence review
          {
            '\n'}
          • Unban Process → Group restoration workflow with logging
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#8e44ad', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  groupCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  groupHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  groupName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  appealStatus: { fontSize: 14, color: '#8e44ad', fontWeight: 'bold' },
  groupDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  categoriesContainer: { marginBottom: 16 },
  categoryCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  categoryName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  categoryDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});