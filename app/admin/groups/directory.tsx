import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function GroupDirectory() {
  const groups = [
    {
      id: 'g001',
      name: 'Tech Enthusiasts',
      type: 'Public',
      members: 1250,
      risk: 'Low',
      status: 'Active',
    },
    {
      id: 'g002',
      name: 'Secret Group Alpha',
      type: 'Secret',
      members: 12,
      risk: 'High',
      status: 'Flagged',
    },
    {
      id: 'g003',
      name: 'Gamers United',
      type: 'Private',
      members: 345,
      risk: 'Medium',
      status: 'Active',
    },
    {
      id: 'g004',
      name: 'Banned Group Example',
      type: 'Public',
      members: 0,
      risk: 'High',
      status: 'Banned',
    },
  ];

  const searchFilters = [
    { name: 'Type', options: ['Public', 'Private', 'Secret'] },
    { name: 'Status', options: ['Active', 'Flagged', 'Banned'] },
    { name: 'Risk Level', options: ['Low', 'Medium', 'High'] },
    { name: 'Member Count', options: ['Any', '<100', '100-1k', '>1k'] },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Group Directory</Text>
        <Text style={styles.subtitle}>Searchable Database of All Groups</Text>
        <Link href="/admin/groups/management-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen provides a searchable database of all groups on the platform, with advanced
          filtering and AI risk indicators to help moderators identify high-risk communities.
        </Text>

        <View style={styles.searchBox}>
          <Text style={styles.searchPlaceholder}>Search by group name or ID...</Text>
        </View>

        <Text style={styles.sectionTitle}>Search Filters:</Text>
        <View style={styles.filtersContainer}>
          {searchFilters.map((filter, index) => (
            <TouchableOpacity key={index} style={styles.filterButton}>
              <Text style={styles.filterText}>{filter.name}: All</Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Group Directory:</Text>
        {groups.map((group) => (
          <Link key={group.id} href={`/admin/groups/${group.id}`} asChild>
            <TouchableOpacity style={styles.groupCard}>
              <View style={styles.groupHeader}>
                <Text style={styles.groupName}>{group.name}</Text>
                <Text style={styles.groupType}>{group.type}</Text>
              </View>
              <Text style={styles.groupDetails}>Members: {group.members}</Text>
              <View style={styles.groupMeta}>
                <Text style={styles.metaText}>Status: {group.status}</Text>
                <Text style={[styles.riskText, { color: group.risk === 'High' ? '#e74c3c' : group.risk === 'Medium' ? '#f39c12' : '#27ae60' }]}>
                  Risk: {group.risk}
                </Text>
              </View>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Advanced group search and filtering</Text>
          <Text style={styles.feature}>• AI risk score indicators</Text>
          <Text style={styles.feature}>• Member count tracking</Text>
          <Text style={styles.feature}>• Activity level monitoring</Text>
          <Text style={styles.feature}>• Bulk management operations</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Group Selection → Group Detail Screen with moderation tools
          {
            '\n'}
          • Risk Assessment → AI risk analysis interface with detailed metrics
          {
            '\n'}
          • Moderation Actions → Group action panels for banning or warning
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  searchBox: { borderWidth: 1, borderColor: '#ddd', borderRadius: 6, padding: 16, marginBottom: 16, backgroundColor: '#fff' },
  searchPlaceholder: { color: '#999', fontSize: 14 },
  filtersContainer: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  filterButton: { backgroundColor: '#fff', padding: 10, borderRadius: 20, marginRight: 8, marginBottom: 8 },
  filterText: { fontSize: 12, color: '#555' },
  groupCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  groupHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  groupName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  groupType: { fontSize: 14, color: '#9b59b6', fontWeight: 'bold' },
  groupDetails: { fontSize: 12, color: '#555', marginBottom: 8 },
  groupMeta: { flexDirection: 'row', justifyContent: 'space-between' },
  metaText: { fontSize: 12, color: '#7f8c8d' },
  riskText: { fontSize: 12, fontWeight: 'bold' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});