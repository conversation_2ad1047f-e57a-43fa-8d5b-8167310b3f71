import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link } from 'expo-router';

export default function GroupCreationRequests() {
  const requests = [
    {
      id: 'gcr001',
      groupName: 'New Tech Startups',
      creator: 'user021',
      payment: 'Paid',
      status: 'Pending Review',
      date: '2024-01-18',
    },
    {
      id: 'gcr002',
      groupName: 'Art & Design Collective',
      creator: 'user022',
      payment: 'Paid',
      status: 'Pending Review',
      date: '2024-01-17',
    },
    {
      id: 'gcr003',
      groupName: 'Local Sports Fans',
      creator: 'user023',
      payment: 'Pending',
      status: 'Awaiting Payment',
      date: '2024-01-16',
    },
  ];

  const requestProcess = [
    { step: 1, name: 'Request Submission', description: 'User submits group creation request' },
    { step: 2, name: 'Payment Verification', description: 'Confirm payment for group creation' },
    { step: 3, name: 'Background Check', description: "Review creator's account history" },
    { step: 4, name: 'Admin Review', description: 'Manual approval/rejection decision' },
    { step: 5, name: 'Group Creation', description: 'Automated group setup upon approval' },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Group Creation Requests</Text>
        <Text style={styles.subtitle}>Manage Pending Group Creations</Text>
        <Link href="/admin/groups/management-dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This screen is for managing pending group creation requests. It includes tools for
          payment verification, creator background checks, and the approval or rejection of requests.
        </Text>

        <Text style={styles.sectionTitle}>Pending Requests:</Text>
        {requests.map((req) => (
          <TouchableOpacity key={req.id} style={styles.requestCard}>
            <View style={styles.requestHeader}>
              <Text style={styles.groupName}>{req.groupName}</Text>
              <Text style={[styles.status, { color: req.status === 'Pending Review' ? '#f39c12' : '#3498db' }]}>
                {req.status}
              </Text>
            </View>
            <Text style={styles.requestDetails}>Creator: {req.creator} | Submitted: {req.date}</Text>
            <Text style={styles.requestDetails}>Payment: {req.payment}</Text>
          </TouchableOpacity>
        ))}

        <Text style={styles.sectionTitle}>Request Process:</Text>
        <View style={styles.processContainer}>
          {requestProcess.map((item) => (
            <View key={item.step} style={styles.processStep}>
              <Text style={styles.stepNumber}>{item.step}</Text>
              <View style={styles.stepInfo}>
                <Text style={styles.stepName}>{item.name}</Text>
                <Text style={styles.stepDescription}>{item.description}</Text>
              </View>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Creation request queue management</Text>
          <Text style={styles.feature}>• Payment verification interface</Text>
          <Text style={styles.feature}>• Request approval/rejection workflow</Text>
          <Text style={styles.feature}>• Creator background checks</Text>
          <Text style={styles.feature}>• Request history tracking</Text>
        </View>

        <Text style={styles.sectionTitle}>Navigation Flow:</Text>
        <Text style={styles.flowDescription}>
          • Request Details → Individual request review interface with creator history
          {
            '\n'}
          • Payment Verification → Payment management screens to confirm transaction
          {
            '\n'}
          • Creator Profile → User detail screen for background check
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#27ae60', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  requestCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12 },
  requestHeader: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  groupName: { fontSize: 16, fontWeight: 'bold', color: '#2c3e50' },
  status: { fontSize: 14, fontWeight: 'bold' },
  requestDetails: { fontSize: 12, color: '#555', marginBottom: 4 },
  processContainer: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  processStep: { flexDirection: 'row', alignItems: 'center', marginBottom: 12 },
  stepNumber: { fontSize: 18, fontWeight: 'bold', color: '#27ae60', marginRight: 12 },
  stepInfo: { flex: 1 },
  stepName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  stepDescription: { fontSize: 12, color: '#555' },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
  flowDescription: { fontSize: 14, color: '#555', lineHeight: 20, backgroundColor: '#fff', padding: 16, borderRadius: 8 },
});