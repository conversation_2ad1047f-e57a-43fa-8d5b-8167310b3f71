import { Link } from 'expo-router';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

export default function GroupManagementDashboard() {
  const keyMetrics = [
    { label: 'Total Groups', value: '45,678', color: '#9b59b6' },
    { label: 'Creation Requests', value: '156 pending', color: '#3498db' },
    { label: 'Flagged Groups', value: '23 requiring review', color: '#f39c12' },
    { label: 'Banned Groups', value: '89 total', color: '#e74c3c' },
  ];

  const groupTypes = [
    { name: 'Public Groups', description: 'Open membership, automatic moderation' },
    { name: 'Private Groups', description: 'Invite-only, automatic moderation' },
    { name: 'Secret Groups', description: 'Hidden, manual review required' },
  ];

  const mainTasks = [
      { 
        title: 'Group Directory', 
        route: '/admin/groups/directory', 
        color: '#3498db',
        description: 'Search, filter, and manage all groups'
    }, { 
        title: 'Creation Requests', 
        route: '/admin/groups/creation-requests', 
        color: '#27ae60',
        description: 'Approve or deny new group creation requests'
    }, { 
        title: 'Secret Groups Monitoring', 
        route: '/admin/groups/secret-monitoring', 
        color: '#e67e22',
        description: 'Monitor activity in secret groups for violations'
    }, {
        title: 'Banned Groups',
        route: '/admin/groups/banned-groups',
        color: '#e74c3c',
        description: 'Manage banned groups and review appeals'
    },
    {
        title: 'Subgroup Management',
        route: '/admin/groups/subgroup-management',
        color: '#16a085',
        description: 'Manage subgroups nested under parent groups (no payment required)'
    },
  ];

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Group Management Dashboard</Text>
        <Text style={styles.subtitle}>Statistics and Management Tools</Text>
        <Link href="/admin/dashboard" asChild>
          <TouchableOpacity style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back to Dashboard</Text>
          </TouchableOpacity>
        </Link>
      </View>

      <View style={styles.content}>
        <Text style={styles.sectionTitle}>Screen Purpose:</Text>
        <Text style={styles.description}>
          This dashboard provides an overview of group statistics and provides access to various
          group management tools, including creation requests, violation reports, and AI risk assessment.
        </Text>

        <Text style={styles.sectionTitle}>Key Metrics:</Text>
        <View style={styles.metricsGrid}>
          {keyMetrics.map((metric, index) => (
            <View key={index} style={[styles.metricCard, { borderTopColor: metric.color }]}>
              <Text style={styles.metricValue}>{metric.value}</Text>
              <Text style={styles.metricLabel}>{metric.label}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Main Tasks:</Text>
        {mainTasks.map((task, index) => (
          <Link key={index} href={task.route} asChild>
            <TouchableOpacity style={[styles.taskCard, { borderLeftColor: task.color }]}>
                <Text style={[styles.taskTitle, { color: task.color }]}>{task.title}</Text>
                <Text style={styles.taskDescription}>{task.description}</Text>
            </TouchableOpacity>
          </Link>
        ))}

        <Text style={styles.sectionTitle}>Group Types:</Text>
        <View style={styles.typesContainer}>
          {groupTypes.map((type, index) => (
            <View key={index} style={styles.typeCard}>
              <Text style={styles.typeName}>{type.name}</Text>
              <Text style={styles.typeDescription}>{type.description}</Text>
            </View>
          ))}
        </View>

        <Text style={styles.sectionTitle}>Features:</Text>
        <View style={styles.featuresList}>
          <Text style={styles.feature}>• Group type distribution analytics</Text>
          <Text style={styles.feature}>• Creation request tracking</Text>
          <Text style={styles.feature}>• Violation report summaries</Text>
          <Text style={styles.feature}>• AI risk assessment overview</Text>
          <Text style={styles.feature}>• Administrative action history</Text>
        </View>

      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f8f9fa' },
  header: { backgroundColor: '#9b59b6', padding: 24, alignItems: 'center' },
  title: { fontSize: 28, fontWeight: 'bold', color: '#fff', marginBottom: 8 },
  subtitle: { fontSize: 16, color: '#f8f9fa', marginBottom: 16 },
  backButton: { backgroundColor: 'rgba(255,255,255,0.2)', paddingHorizontal: 16, paddingVertical: 8, borderRadius: 20 },
  backButtonText: { color: '#fff', fontSize: 14 },
  content: { padding: 24 },
  sectionTitle: { fontSize: 20, fontWeight: 'bold', color: '#2c3e50', marginTop: 24, marginBottom: 12 },
  description: { fontSize: 16, color: '#34495e', lineHeight: 24, marginBottom: 16 },
  metricsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', marginBottom: 16 },
  metricCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, width: '48%', marginBottom: 12, borderTopWidth: 4, alignItems: 'center' },
  metricValue: { fontSize: 22, fontWeight: 'bold', color: '#2c3e50' },
  metricLabel: { fontSize: 12, color: '#7f8c8d', textAlign: 'center', marginTop: 4 },
  taskCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 12, borderLeftWidth: 4 },
  taskTitle: { fontSize: 18, fontWeight: 'bold' },
  taskDescription: { fontSize: 14, color: '#555', marginTop: 4 },
  typesContainer: { marginBottom: 16 },
  typeCard: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 8 },
  typeName: { fontSize: 14, fontWeight: 'bold', color: '#2c3e50' },
  typeDescription: { fontSize: 12, color: '#555', marginTop: 4 },
  featuresList: { backgroundColor: '#fff', padding: 16, borderRadius: 8, marginBottom: 16 },
  feature: { fontSize: 14, color: '#555', marginBottom: 8 },
});